@extends('layouts.master')
@section('title')
    Verify Quote
@endsection
@section('css')
    <!-- DataTables -->
    <link href="{{ URL::asset('/assets/libs/datatables/datatables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/dropzone/dropzone.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ URL::asset('/assets/libs/datepicker/datepicker.min.css') }}">
@endsection

@section('content')
    {{-- Customise issue on table element top border color --}}
    <style>
        #package-summary td {
            border-top: 0.1rem #eee solid !important;
        }
        /* Remove border, background, and outline for the input */
        .plain-input {
            border: none;
            background: transparent;
            box-shadow: none;
        }

        /* Remove the outline on focus */
        .plain-input:focus {
            outline: none;
            box-shadow: none;
        }

        /* Remove the hover effect */
        .plain-input:hover {
            border: none;
        }
    </style>
    @component('common-components.breadcrumb')
        @slot('pagetitle') Order Management @endslot
        @slot('title') Verify Quote @endslot
    @endcomponent
    
{{-- <small class="text-muted">Modify header to add instructions and * for required fields</small> --}}
<div class="row">
    <div class="col-xl-12">
        <div class="custom-accordion">
            <div class="card mb-2">
                <a href="#quote-customerinfo-collapse" class="text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-receipt text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        01
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mt-2">Customer Details</h5>
                                {{-- <p class="text-muted text-truncate mb-0">Sed ut perspiciatis unde omnis iste</p> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <div id="quote-customerinfo-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row d-none">
                                    <label for="quote_id" class="col-md-4 col-form-label">Quote Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Quote Id." id="quote_id" name="quote_id" value="{{ $quote->id ?? '' }}">
                                        <small id="quote_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="customer_name" class="col-md-4 col-form-label">Customer Name</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Customer Name" id="customer_name" name="customer_name" value="{{ $quote->customer_name ?? '' }}" disabled>
                                        <small id="customer_name_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sfdc_id" class="col-md-4 col-form-label">SFDC Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="SFDC Id." id="sfdc_id" name="sfdc_id" value="{{ $quote->sfdc_id ?? '' }}" disabled>
                                        <small id="sfdc_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="brn" class="col-md-4 col-form-label">BRN</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="BRN" id="brn" name="brn" value="{{ $quote->brn ?? '' }}" disabled>
                                        <small id="brn_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="vertical" class="col-md-4 col-form-label">Vertical</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="vertical" name="vertical" disabled>
                                                    <option value="">Select Vertical...</option>
                                                    @foreach(\App\Models\Vertical::all() as $vertical)
                                                        <option value="{{ $vertical->name }}" {{ (isset($quote->vertical->name) && $quote->vertical->name == $vertical->name) ? 'selected' : '' }}>
                                                            {{ $vertical->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="vertical_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="vertical_others" name="vertical_others" value="{{ $quote->vertical_others ?? '' }}" disabled>
                                                <small id="vertical_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sale_segment" class="col-md-4 col-form-label">Sale Segment</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="sale_segment" name="sale_segment" disabled>
                                                    <option value="">Select Sale Segment...</option>
                                                    @foreach(\App\Models\SaleSegment::all() as $saleSegment)
                                                        <option value="{{ $saleSegment->name }}" {{ (isset($quote->sale_segment) && $quote->sale_segment->name == $saleSegment->name) ? 'selected' : '' }}>
                                                            {{ $saleSegment->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="sale_segment_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="sale_segment_others" name="sale_segment_others" value="{{ $quote->sale_segment_others ?? '' }}" disabled>
                                                <small id="sale_segment_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row">
                                    <label for="person_in_charge" class="col-md-4 col-form-label">Person In Charge</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Person In Charge" id="person_in_charge" name="person_in_charge" value="{{ $quote->person_in_charge ?? '' }}" disabled>
                                        <small id="person_in_charge_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="position_title" class="col-md-4 col-form-label">Position Title</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Position Title" id="position_title" name="position_title" value="{{ $quote->position_title ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="department" class="col-md-4 col-form-label">Department</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Department" id="department" name="department" value="{{ $quote->department ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contact_no" class="col-md-4 col-form-label">Telephone No.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Telephone No." id="contact_no" name="contact_no" value="{{ $quote->contact_no ?? '' }}" disabled>
                                        <small id="contact_no_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-center d-none">
                                    <label for="prepared_by" class="col-md-4 col-form-label">Prepared By</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Prepared By" id="prepared_by" name="prepared_by" value="{{ Str::ucfirst(optional(Auth::user())->name ?? '') }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="date" class="col-md-4 col-form-label">Date</label>
                                    <div class="col-md-8">
                                        <div class="input-group" id="datepicker">
                                            <input type="text" class="form-control" placeholder="Date" data-date-format="dd-mm-yyyy" data-date-container='#datepicker' data-provide="datepicker" name="date" id="date" value="{{ Carbon\Carbon::parse($quote->created_at)->format('d-m-Y') ?? '' }}" disabled>
                                            <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                        </div><!-- input-group -->
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <div class="mb-0 mb-md-1 row">
                                    <label for="unit_street" class="col-md-2 col-form-label">Address</label>
                                    <div class="col-md-5 px-md-1">
                                        <input class="form-control" type="text" placeholder="Unit No, Street Name" id="unit_street" name="unit_street" value="{{ $quote->address->unit_street ?? '' }}" disabled>
                                        <small id="unit_street_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-5 ps-md-0">
                                        <input class="form-control" type="text" placeholder="Housing Area / Development Name" id="housing_area" name="housing_area" value="{{ $quote->address->housing_area ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="postcode" class="col-md-2 col-form-label d-none d-md-block"></label>
                                    <div class="col-md-3 px-md-1">
                                        <input class="form-control" type="number" placeholder="Postcode" id="postcode" name="postcode" value="{{ $quote->address->postcode ?? '' }}" disabled>
                                        <small id="postcode_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-3 ps-md-0 pe-md-1">
                                        <input class="form-control" type="text" placeholder="City" id="city" name="city" value="{{ $quote->address->city ?? '' }}" disabled>
                                        <small id="city_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-4 ps-md-0">
                                        <input class="form-control" type="text" placeholder="State" id="state" name="state" value="{{ $quote->address->state ?? '' }}" disabled>
                                        <small id="state_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-package-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-truck text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        02
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Package</h5>
                                {{-- <p class="text-muted text-truncate mb-0">Neque porro quisquam est</p> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-package-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contract_length" class="col-md-4 col-form-label">Contract (Months)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Contract in months" id="contract_length" name="contract_length" min="1" step="1" value="{{ $quote->contract_length ?? '' }}" disabled>
                                        <small id="contract_length_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="col-12">
                                <div class="px-md-4 pb-4">
                                    <h5 class="font-size-14 my-3">Package Summary</h5>
                                    <p id="packages_error" class="text-danger error-message mb-1" style="display: none"></p>
                                    <table id="package-summary" class="table table-bordered nowrap" style="font-size: 0.8rem; border-collapse: collapse; border-spacing: 0; width: 100%;">
                                        <thead>
                                            <tr>
                                                <th>Table Id.</th>
                                                <th>Type</th>
                                                <th>Package</th>
                                                <th>Provider</th>
                                                <th>No. of User</th>
                                                <th>Price Id.</th>
                                                <th>Price (RM)</th>
                                                <th>Discount (%)</th>
                                                <th>Net Price (RM)</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be dynamically inserted here -->
                                        </tbody>
                                    </table>
                                    <div class="row justify-content-md-end">
                                        <div class="col-md-5 px-4">                        
                                            <h5 class="font-size-14 mb-3 mt-4">Price Summary</h5>
                                            <div class="mb-0 row">
                                                <label for="price_per_user" class="col-form-label col-6">Price (per user):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="price_per_user" name="price_per_user">
                                                </div>
                                            </div>
                                            <div class="mb-0 row">
                                                <label for="price_monthly" class="col-form-label col-6">Total Price (Monthly):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="price_monthly" name="price_monthly">
                                                </div>
                                            </div>
                                            <div class="mb-0 pb-2 row border-bottom">
                                                <label for="total_discount" class="col-form-label col-6">Total Discount (Monthly):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_discount" name="total_discount">
                                                </div>
                                            </div>
                                            {{-- <div class="mb-2 row border-bottom">
                                                <label for="discount_tier" class="col-form-label col-6">Discount Tier:</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="Discount Tier" id="discount_tier" name="discount_tier">
                                                </div>
                                            </div> --}}
                                            <div class="mb-0 mt-2 row">
                                                <label for="total_contract" class="col-form-label col-6">Subtotal Price (Contract):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_contract" name="total_contract">
                                                </div>
                                            </div>
                                            <div class="mb-0 pb-2 row border-bottom">
                                                <label for="tax" class="col-form-label col-6">Applicable Tax (8%):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="tax" name="tax">
                                                </div>
                                            </div>
                                            <div class="mb-0 mt-2 row">
                                                <label for="total_price" class="col-form-label col-6">Total Price (inclusive tax):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_price" name="total_price">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>  
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-discount-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        03
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mt-2">Discount Tier</h5>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-discount-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        {{-- <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-1 row">
                                    <label for="package_discount" class="col-md-4 col-form-label">Package Discount (%)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Package Discount (%)" id="package_discount" name="package_discount" min="0" max="100" value="{{ $quote->package_discount * 100 ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-1 row">
                                    <label for="subpackage_discount" class="col-md-4 col-form-label">Subpackage Discount (%)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Subpackage Discount (%)" id="subpackage_discount" name="subpackage_discount" min="0" max="100" value="{{ $quote->subpackage_discount * 100 ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <hr class="hr-dashed hr-menu">
                            </div>
                        </div> --}}
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row d-none">
                                    <label for="tier" class="col-md-4 col-form-label">Discount Tier Id</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Discount Tier Id" id="discount_tier_id" name="discount_tier_id" value="{{ $quote->discount_tier->id ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="tier" class="col-md-4 col-form-label">Tier</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Tier" id="tier" name="tier" value="{{ $quote->discount_tier->tier_name ?? '' }}" disabled>
                                        <small id="tier_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="customer_name" class="col-md-4 col-form-label">Level Authority</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Level Authority" id="level_authority" name="level_authority" value="{{ $quote->discount_tier->authority_level ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row {{ $quote->attachments->isEmpty() ? 'd-none' : '' }}">
                                    <label for="tier" class="col-md-4 col-form-label">Supporting Document</label>
                                    <div class="col-md-8">
                                        {{-- Add a link to download existing attachment --}}
                                        @foreach ($quote->attachments as $attachment)
                                            @if($attachment->description == 'Discount Supporting Document')
                                                <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                    {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                </a>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-recipient-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        04
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Email Recipient List</h5>
                                {{-- <small class="text-muted text-truncate"><i>Review and select staff name list to get email notifications (allow multiple selection)</i></small> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-recipient-collapse" class="collapse">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="staff" class="col-md-3 col-form-label">List of Staff (AM/Sales)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple" name="staff[]" id="staff" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%" disabled>
                                            @foreach (json_decode($quote->recipient_email) as $staff)
                                                <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endforeach
                                        </select>
                                        <small id="recipient_email_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-handover-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        05
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mt-2">Handover Documents</h5>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-handover-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12 px-4">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 5%">Checklist</th>
                                            <th style="width: 40%">Documents</th>
                                            <th style="width: 55%">Uploaded Files</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Official LOA/PO - REMOVED FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" id="loaCheckbox" data-section="loa" disabled checked>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">Official LOA/PO</label>
                                            </td>
                                            <td>
                                                <div id="loaSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'Official LOA/PO Document')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr> -->

                                        <!-- Business Case - REMOVED FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="businessCaseCheckbox" data-section="businessCase" checked>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">Business Case/Discount Approval/Financial Analysis</label>
                                            </td>
                                            <td>
                                                <div id="businessCaseSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'Business Case/Discount Approval Document')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr> -->

                                        <!-- Final Proposal -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="proposalCheckbox" data-section="proposal" checked>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">Final Proposal/Quotation submitted to customer</label>
                                            </td>
                                            <td>
                                                <div id="proposalSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'Final Proposal/Quotation Document')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Business Arrangement - REMOVED FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="arrangementCheckbox" data-section="arrangement" checked>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">Business Arrangement Form</label>
                                            </td>
                                            <td>
                                                <div id="arrangementSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'Business Arrangement Form')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr> -->

                                        <!-- PO/LOA to partner -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="partnerCheckbox" data-section="partner" checked>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">PO/LOA to partner</label>
                                            </td>
                                            <td>
                                                <div id="partnerSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'PO/LOA to Partner Document')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Other Document -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="otherDocumentCheckbox" data-section="otherDocument"
                                                    @php
                                                        $hasOtherDocument = $quote->attachments->where('description', 'Other Document')->isNotEmpty();
                                                    @endphp
                                                    {{ $hasOtherDocument ? 'checked' : '' }}>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3 align-middle">
                                                <label class="form-label fw-bold mb-0">Other Document</label>
                                            </td>
                                            <td>
                                                <div id="otherDocumentSelectedFiles" class="selected-files">
                                                @foreach ($quote->attachments as $attachment)
                                                    @if($attachment->description == 'Other Document')
                                                        <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                            {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                        </a>
                                                    @endif
                                                @endforeach
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-team-member-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        06
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Personal In Charge</h5>
                                <small class="text-muted text-truncate"><i>Review and select staff name to get email notifications (allow multiple selection)</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-team-member-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sd" class="col-md-3 col-form-label">*List of Staff (SD)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="sd[]" id="sd" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($quote->project_team) as $staff)
                                            {{-- Add if role is SD --}}
                                            @if($staff->role == 'SD')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="sd_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="pm" class="col-md-3 col-form-label">*List of Staff (PM)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="pm[]" id="pm" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($quote->project_team) as $staff)
                                            {{-- Add if role is PM --}}
                                            @if($staff->role == 'PM')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="pm_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="tad" class="col-md-3 col-form-label">*List of Staff (TAD)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="tad[]" id="tad" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($quote->project_team) as $staff)
                                            {{-- Add if role is TAD --}}
                                            @if($staff->role == 'TAD')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="tad_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sc" class="col-md-3 col-form-label">*List of Staff (SC)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="sc[]" id="sc" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($quote->project_team) as $staff)
                                            {{-- Add if role is SC --}}
                                            @if($staff->role == 'SC')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="sc_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sc" class="col-md-3 col-form-label">*List of Staff (CM)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="cm[]" id="cm" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($quote->project_team) as $staff)
                                            {{-- Add if role is CM --}}
                                            @if($staff->role == 'CM')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="sc_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-remark-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        07
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Return Quote</h5>
                                <small class="text-muted text-truncate"><i>Fill in remarks (required for return quote)</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-remark-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="return_remark" class="col-md-3 col-form-label">Remarks</label>
                                    <div class="col-md-8">
                                        <textarea name="return_remark" id="return_remark" class="form-control" cols="30" rows="4"></textarea>
                                        <small id="return_remark_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="row my-4">
            <div class="col">
                <div class="text-end mt-2 mt-sm-0">
                    <btn class="btn btn-info" id="btn-generate-quote">Generate Quotation</btn>
                    <btn class="btn btn-secondary" id="btn-return-quote">Return</btn>
                    <btn class="btn btn-primary" id="btn-handover-quote">Handover</btn>
                    <btn class="btn btn-light" id="btn-back">Back</btn>
                </div>
            </div> <!-- end col -->
        </div> <!-- end row-->

        {{-- Toast --}}
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="align-items-center text-white bg-success border-0">
                    <div class="d-flex">
                        <div class="toast-body" id="success-toast-message">
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="align-items-center text-white bg-danger border-0">
                    <div class="d-flex">
                        <div class="toast-body" id="failed-toast-message">
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- end row -->

@endsection
@section('script')
    <script src="{{ URL::asset('/assets/libs/datatables/datatables.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/jszip/jszip.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/pdfmake/pdfmake.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/datatables.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/select2/select2.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/dropzone/dropzone.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/ecommerce-add-product.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/datepicker/datepicker.min.js') }}"></script>    

    <script>
        $(document).ready(function() {

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Click 'Back' button
            $('#btn-back').click(function() {
                window.history.back();
            });

            // Testing Purpose
            // $.ajax({
            //     url: "{{ route('postcode', ['postcode' => '26500']) }}",
            //     method: 'GET',
            //     success: function(data) {
            //         // Handle the response data
            //         console.log(data);
            //         // You can update the DOM with the fetched data here
            //     }
            // })

            // initialize the table
            var table = $('#package-summary').DataTable({
                paging: false,    // Disable paging
                ordering: false,  // Disable sorting
                searching: false,  // Disable searching
                info: false,      // Disable info
                scrollX: true,
                processing: true,
                columns: [
                    { data: "table_id", visible: false },  // Hide column
                    { data: "type", visible: false },  // Hide column
                    { data: "package" },                    
                    { data: "provider" },                    
                    { data: "no_user" },   
                    { data: "price_id", visible: false },  // Hide columns             
                    { data: "price" },                    
                    { data: "discount" },                    
                    { data: "net_price" },                    
                    { data: "action" },                    
                ]
            });

            // adjust table design
            $('a[href="#quote-summary-collapse"], a[href="#quote-package-collapse"]').on('click', function() {
                table.columns.adjust().draw();
            });

            // Auto-populate quote id
            var quote = @json($quote);
            $('.page-title').append(' [' + quote.quote_id + ']');

            // Auto-populate the package/subpackage table
            var quoteItem = @json($quote->quote_items ?? []);
            // console.log(quoteItem);

            quoteItem.forEach(function(item) {

                var data = {
                    table_id: item.quotable_id,
                    type: item.quotable_type.split('\\').pop().toLowerCase(),
                    package: item.description,
                    provider: item.provider,
                    no_user: item.quantity ?? '',
                    price_id: item.price.id,
                    price: item.discount_percentage > 0 ? (item.quoted_price / (1 - item.discount_percentage)).toFixed(2) : item.quoted_price, 
                    discount: item.discount_percentage*100,
                    net_price: item.quoted_price,
                    // action: item.quotable_type.includes('Package') ? "<a href='javascript:void(0);' name='btn-delete' class='px-2 text-secondary'><i class='uil uil-trash-alt font-size-14'></i></a>" : ""
                    action: ""
                }
                
                table.row.add(data).draw();
            });

            // // Auto-create the package options
            $.ajax({
                url: "{{ route('packages.provider') }}",
                method: 'GET',
                success: function(data) {
                    // Handle the response data
                    // console.log(data);
                    // You can update the DOM with the fetched data here
                    // Clear the current options (optional)
                    $('#package').empty();
                    
                    // Append a default "Select Package..." option
                    $('#package').append("<option value=''>Select Package...</option>");

                    // Loop through the response and append each package as an option
                    $.each(data.packages, function(index, package) {
                        $('#package').append('<option value="' + package.id + '">' + package.name + '</option>');
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching packages:', error);
                }
            });

            // select postcode, reflect city and state
            $('#postcode').change(function() {
                var selectedValue = $(this).val();
                var route = "{{ route('postcode', ['postcode' => 'number']) }}";
                let modifiedRoute = route.replace("number", selectedValue);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Handle the response data
                        // console.log(data);
                        // You can update the DOM with the fetched data here
                        $('#city').val(data.city);
                        $('#state').val(data.state.name);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching postcode:', error);
                    }
                });
            });

            // select package, reflect price
            $('#package').change(function() {
                var selectedValue = $(this).val();
                var route = "{{ route('package.details', ['packageId' => 'package_id', 'packageName' => 'package_name']) }}";
                let modifiedRoute = route.replace("package_id", selectedValue).replace("package_name", $('#package option:selected').text());
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Handle the response data
                        console.log(data);
                        // You can update the DOM with the fetched data here
                        $('#package_price').val(data.price);
                        $('#package_price_id').val(data.package_price_id);
                        $('#f2f_price').val(data.subpackages[0].price_fixed);
                        $('#f2f_price_id').val(data.subpackages[0].price_fixed_id);
                        $('#f2m_price').val(data.subpackages[0].price_mobile);
                        $('#f2m_price_id').val(data.subpackages[0].price_mobile_id);
                        $('#provider').val(data.provider !== 'Others' ? data.provider : '');
                        $('#subpackage').val(data.subpackages[0].name);
                        $('#package_id').val(data.id);
                        $('#subpackage_id').val(data.subpackages[0].id);
                    }
                });
            });

            // select subpackage, reflect price
            $('#subpackage_discount').change(function() {
                var subpackage_discount = $(this).val();
                var discount = $('#package_discount').val();

                var final_discount = (subpackage_discount >= discount) ? subpackage_discount : discount;
                // console.log('final discount: ', final_discount);

                var route = "{{ route('discount.tier', ['discount' => 'discountValue']) }}";
                let modifiedRoute = route.replace("discountValue", final_discount);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Auto populate the tier and level authority fields
                        $('#tier').val(data.tier.tier_name);
                        $('#level_authority').val(data.tier.authority_level);
                        $('#discount_tier_id').val(data.tier.id);
                    }
                });

                // update discount value & net price in the table
                // Get the discount value from the form input
                var discount = parseFloat($('#subpackage_discount').val()) || 0; // Default to 0 if invalid

                // Loop through each row in the table and update the net price
                if (table.rows().count() > 0) {
                    table.rows().every(function() {
                        var row = this.node(); // Current row
                        var packageName = $(row).find('td:eq(0)').text().toLowerCase(); // Get package name from the first column

                        // Skip iteration if package name contains 'fixed' or 'mobile'
                        if (packageName.includes('fixed') || packageName.includes('mobile')) {
                            var originalPrice = parseFloat($(row).find('td:eq(3)').text()); // Get Original Price from the second column
                            var netPrice = originalPrice - (originalPrice * discount / 100); // Calculate the Net Price

                            // Update the Net Price in the table row (fourth column)
                            $(row).find('td:eq(5)').text(netPrice.toFixed(2)); // Format to 2 decimal places

                            // Update the discount in the row (third column) if needed
                            $(row).find('td:eq(4)').text(discount); // Show the current discount in the row

                            // Update internal row data
                            var rowData = table.row(row).data();
                            rowData.net_price = netPrice.toFixed(2); // Update net_price
                            rowData.discount = discount; // Update discount
                            table.row(row).data(rowData); // Save updated data to DataTable
                        }
                        else {
                            return;
                        }
                    });
                }
            })

            // fill in discount %, reflect tier and level authority
            $('#package_discount').change(function() {
                var discount = $(this).val();
                var subpackage_discount = $('#subpackage_discount').val();

                var final_discount = (discount >= subpackage_discount) ? discount : subpackage_discount;
                // console.log('final discount: ', final_discount);

                var route = "{{ route('discount.tier', ['discount' => 'discountValue']) }}";
                let modifiedRoute = route.replace("discountValue", final_discount);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // console.log(data);
                        // Auto populate the tier and level authority fields
                        $('#tier').val(data.tier.tier_name);
                        $('#level_authority').val(data.tier.authority_level);
                        $('#discount_tier_id').val(data.tier.id);
                    }

                });
                // update discount value & net price in the table
                // Get the discount value from the form input
                var discount = parseFloat($('#package_discount').val()) || 0; // Default to 0 if invalid

                // Loop through each row in the table and update the net price
                if (table.rows().count() > 0) {
                    table.rows().every(function() {
                        var row = this.node(); // Current row
                        var packageName = $(row).find('td:eq(0)').text().toLowerCase(); // Get package name from the first column

                        // Skip iteration if package name contains 'fixed' or 'mobile'
                        if (packageName.includes('fixed') || packageName.includes('mobile')) {
                            return;
                        }

                        var originalPrice = parseFloat($(row).find('td:eq(3)').text()); // Get Original Price from the second column
                        var netPrice = originalPrice - (originalPrice * discount / 100); // Calculate the Net Price

                        // Update the Net Price in the table row (fourth column)
                        $(row).find('td:eq(5)').text(netPrice.toFixed(2)); // Format to 2 decimal places

                        // Update the discount in the row (third column) if needed
                        $(row).find('td:eq(4)').text(discount); // Show the current discount in the row

                        // Update internal row data
                        var rowData = table.row(row).data();
                        rowData.net_price = netPrice.toFixed(2); // Update net_price
                        rowData.discount = discount; // Update discount
                        table.row(row).data(rowData); // Save updated data to DataTable
                    });

                    // Re-calculate price summary
                    calculatePriceSummary();
                }

            });

            // Add package to the summary table
            $("#btn-add-package").click(function() {

                // Validate the form
                var isValid = true;

                // Clear previous error messages
                $('.error-message').hide();

                // Validate each required field
                $('#package, #no_user, #subpackage, #provider').each(function() {
                    var field = $(this);
                    var errorMessage = $('#' + field.attr('id') + '_error'); // Find corresponding error message element
                    if (field.attr('id') == 'no_user') {
                        if (field.val() < 10) {
                            isValid = false; // If field is empty, set isValid to false
                            errorMessage.text('Minimum number of users is 10.').show(); // Show the error message
                        }
                    }
                    // else if (field.attr('id') == 'contract_length') {
                    //     if (field.val() < 1) {
                    //         isValid = false; // If field is empty, set isValid to false
                    //         errorMessage.text('Minimum number of contract is 1 month.').show(); // Show the error message
                    //     }
                    // }
                    else {
                        if (field.val().trim() === '') {
                            isValid = false; // If field is empty, set isValid to false
                            errorMessage.text('This field is required.').show(); // Show the error message
                        }

                    } 
                });

                // If the form is valid, submit it
                if (isValid) {

                    var rowData1 = {
                        table_id: $('#package_id').val(),
                        type: 'package',
                        package: $('#package option:selected').text(),
                        provider: $('#provider').val(),
                        no_user: $('#no_user').val(),
                        price_id: $('#package_price_id').val(),
                        price: $('#package_price').val(),
                        discount: $('#package_discount').val(),
                        net_price: ($('#package_discount').val() > 0) ? (parseFloat($('#package_price').val()) - (parseFloat($('#package_discount').val()) / 100 * parseFloat($('#package_price').val()))).toFixed(2): $('#package_price').val(),
                        action: "<a href='javascript:void(0);' name='btn-delete' class='px-2 text-secondary disabled'><i class='uil uil-trash-alt font-size-14'></i></a>"
                    };

                    var rowData2 = {
                        table_id: $('#subpackage_id').val(),
                        type: 'subpackage',
                        package: `${$('#subpackage').val()} - Fixed to Fixed`,
                        provider: $('#provider').val(),
                        no_user: "",
                        price_id: $('#f2f_price_id').val(),
                        price: $('#f2f_price').val(),
                        discount: $('#subpackage_discount').val(),
                        net_price: $('#f2f_price').val(),
                        // action: "<a href='javascript:void(0);' name='btn-delete' class='px-2 text-secondary'><i class='uil uil-trash-alt font-size-14'></i></a>"
                        action: ""
                    };

                    var rowData3 = {
                        table_id: $('#subpackage_id').val(),
                        type: 'subpackage',
                        package: `${$('#subpackage').val()} - Fixed to Mobile`,
                        provider: $('#provider').val(),
                        no_user: "",
                        price_id: $('#f2m_price_id').val(),
                        price: $('#f2m_price').val(),
                        discount: $('#subpackage_discount').val(),
                        net_price: ($('#subpackage_discount').val() > 0) ? (parseFloat($('#f2m_price').val()) - (parseFloat($('#subpackage_discount').val()) / 100 * parseFloat($('#f2m_price').val()))).toFixed(2): $('#f2m_price').val(),
                        // action: "<a href='javascript:void(0);' name='btn-delete' class='px-2 text-secondary'><i class='uil uil-trash-alt font-size-14'></i></a>"
                        action: ""
                    };

                    // Add the new rows to the table
                    table.row.add(rowData1).draw();
                    table.row.add(rowData2).draw();
                    table.row.add(rowData3).draw();

                    // Reset required fields
                    $('#package, #provider, #package_price, #f2f_price, #f2m_price').val('');
                    $('#no_user').val(10);

                    // Calculate price summary
                    calculatePriceSummary();

                    // Show the toast
                    $('#success-toast-message').text('Package and subpackage added successfully!');
                    var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                    var toast = new bootstrap.Toast(toastElement, {
                        autohide: true,     // Automatically hide after the delay
                        delay: 2000         // Time in milliseconds before the toast hides
                    });
                    toast.show();  // This show the toast   

                } 
                else {
                    // Show the toast
                    $('#failed-toast-message').text('Please fill in all required fields.');
                    var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                    var toast = new bootstrap.Toast(toastElement, {
                        autohide: true,     // Automatically hide after the delay
                        delay: 2000         // Time in milliseconds before the toast hides
                    });
                    toast.show();  // This show the toast
                }
            });

            // Delete button functionality
            table.on('click', '[name="btn-delete"]', function() {
                var row = $(this).closest('tr'); // Find the row
                var rowIndex = table.row(row).index(); // Get the index of the row

                // Remove the row and the subsequent 2 rows
                table.row(rowIndex).remove().draw();
                table.row(rowIndex).remove().draw();
                table.row(rowIndex).remove().draw();

                // Re-calculate price summary
                calculatePriceSummary();
            });

            // improve table design
            $('a[href="#quote-summary-collapse"]').on('click', function() {
                table.columns.adjust().draw();
            });

            // change contract will reflect to total contract
            $('#contract_length').change(function() {
                calculatePriceSummary();
            });

            // Count Price Summary
            function calculatePriceSummary() {
                // console.log('Recalculating price summary...');
                let pricePerUser = 0;
                let subTotalPrice = 0;
                let totalDiscount = 0;

                // Iterate through all rows in the table
                table.rows().every(function() {

                    var row = this.node(); // Current row
                    var packageName = $(row).find('td:eq(0)').text().toLowerCase(); // Get package name from the first column

                    // Skip iteration if package name contains 'fixed' or 'mobile'
                    if (packageName.includes('fixed') || packageName.includes('mobile')) {
                        return;
                    }

                    var netPrice = parseFloat($(row).find('td:eq(3)').text()); // Get Net Price from the fifth column
                    var noUser = parseFloat($(row).find('td:eq(2)').text()); // Get No. of User from the third column
                    var discount = parseFloat($(row).find('td:eq(4)').text()) || 0; // Get Discount from the fourth column, assign to 0 if no value
                    var rowTotalDiscount = discount == 0 ? 0 : (netPrice * discount / 100 * noUser); // Assign 0 if no value

                    pricePerUser += netPrice;
                    subTotalPrice += netPrice * noUser;
                    totalDiscount += rowTotalDiscount;
                });

                $('#price_per_user').val(pricePerUser ? 'RM ' + pricePerUser.toFixed(2) : '');
                $('#price_monthly').val(subTotalPrice ? 'RM ' + subTotalPrice.toFixed(2) : '');
                $('#total_discount').val(totalDiscount ? 'RM ' + totalDiscount.toFixed(2) : '');
                $('#total_monthly').val(subTotalPrice || totalDiscount ? 'RM ' + (subTotalPrice - totalDiscount).toFixed(2) : '');
                $('#total_contract').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val()).toFixed(2) : '');
                $('#tax').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val() * 0.08).toFixed(2) : '');
                $('#total_price').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val() * 1.08).toFixed(2) : '');
            }

            // Recalculate when table data changes
            table.on('draw', calculatePriceSummary());

            // Disable the staff field
            $('#staff').addClass('disabled');
            $('#sd').addClass('disabled');
            $('#sc').addClass('disabled');
            $('#pm').addClass('disabled');
            $('#tad').addClass('disabled');

            // Initialize Select2 for Search
            $('#staff').select2({
                placeholder: "Search for a staff...",
                ajax: {
                    url: "{{ route('staff.search') }}",  // Your API route
                    dataType: 'json',
                    delay: 250,  // Delay for API calls to prevent too many requests
                    data: function(params) {
                        return {
                            info: params.term,  // The search term entered by the user
                        };
                    },
                    processResults: function(data) {
                        // Map the API response into the format that Select2 expects
                        return {
                            results: data.results.map(function(item) {
                                return {
                                    id: item.Name + ' (' + item.email + ')',  // Use Staff_No as the ID
                                    text: item.Name + ' (' + item.email + ')'    // Display "Staff_No - Name" as the text
                                };
                            })
                        };
                    },
                    cache: true
                },
                multiple: true,  // Allow multiple selections
                minimumInputLength: 2  // Start showing results after 2 characters
            });

            // $('#quote-recipient-collapse').on('shown.bs.collapse', function () {
            //     // After the collapsible section is shown, trigger the resize on Select2
            //     $('#staff').trigger('change');
            // });

            // Generate Quotation
            $('#btn-generate-quote').on('click', function() {

                // Disable the button to prevent multiple clicks and change the text to "Generating..."
                $(this).addClass('disabled').text('Generating...');

                // Get customer details
                var dataCustomer = {
                    id: $('#quote_id').val(),
                    customer: $('#customer_name').val(),
                    address: $('#unit_street').val() ? `${$('#unit_street').val().replace(/,\s*$/, '')}, ${$('#housing_area').val().replace(/,\s*$/, '')}, ${$('#postcode').val()}, ${$('#city').val()}, ${$('#state').val()}` : '',
                    pic: $('#person_in_charge').val(),
                    title_department: $('#department').val() ? `${$('#position_title').val()}, ${$('#department').val()}` : $('#position_title').val(),
                    contact_no: $('#contact_no').val(),
                    sfdc_id: $('#sfdc_id').val(),
                    brn: $('#brn').val(),
                    date: $('#date').val(),
                    prepared_by: $('#prepared_by').val(),
                }

                // Check if all Customer details are filled
                // var allFieldsFilled = Object.values(dataCustomer).every(function(value) {
                //     return value !== '' && value !== null && value !== undefined;
                // });

                // if (!allFieldsFilled) {
                //     // Show the toast
                //     $('#failed-toast-message').text('Please fill in all customer details before generating the quotation.');
                //     var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                //     var toast = new bootstrap.Toast(toastElement, {
                //         autohide: true,     // Automatically hide after the delay
                //         delay: 3000         // Time in milliseconds before the toast hides
                //     });
                //     toast.show();  // This shows the toast
                //     return;  // Exit the function if not all fields are filled
                // }

                var dataOther = {
                    contract: `${$('#contract_length').val()} months`,
                    price_per_user: $('#price_per_user').val() ? parseFloat($('#price_per_user').val().replace('RM ', '')).toFixed(2) : null,
                    price_monthly: $('#price_monthly').val() ? parseFloat($('#price_monthly').val().replace('RM ', '')).toFixed(2) : null,
                    total_discount: $('#total_discount').val() ? parseFloat($('#total_discount').val().replace('RM ', '')).toFixed(2) : null,
                    total_contract: $('#total_contract').val() ? parseFloat($('#total_contract').val().replace('RM ', '')).toFixed(2) : null,
                    tax: $('#tax').val() ? parseFloat($('#tax').val().replace('RM ', '')).toFixed(2) : null,
                    total_price: $('#total_price').val() ? parseFloat($('#total_price').val().replace('RM ', '')).toFixed(2) : null,
                };

                // Combine all data into one object
                var requestData = {
                    ...dataCustomer,
                    ...dataOther,
                    datatableData: table.rows().data().toArray()
                };

                $.ajax({
                    url: "{{ route('quote.generate') }}",
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: requestData,
                    success: function(response) {
                        if (response.success) {
                            // console.log(response);
                            // Download PDF
                            downloadPDF(response.download_url, response.filename);

                            // Enable the button and change the text back to "Generate Quotation"
                            $('#btn-generate-quote').removeClass('disabled').text('Generate Quotation');
                            
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            $('#error-messages').removeClass('d-none').html(errorHtml);
                        } else {
                            $('#error-messages')
                                .removeClass('d-none')
                                .html('An error occurred while generating the PDF.');
                        }

                        // Enable the button and change the text back to "Generate Quotation"
                        $('#btn-generate-quote').removeClass('disabled').text('Generate Quotation');
                    },
                });
            });

            // Auto download PDF function
            function downloadPDF(url, filename) {
                // Create blob link to download
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', filename);
                link.setAttribute('target', '_blank');
                // Add link to body
                document.body.appendChild(link);
                // Start download
                link.click();
                // Clean up
                document.body.removeChild(link);
            }

            // Handover Quotation
            $('#btn-handover-quote').on('click', function() {
                // Disable the button and change the text to "Please wait..."
                $('#btn-handover-quote').addClass('disabled').text('Handovering...');
                var quoteId = $('#quote_id').val();
                var route = "{{ route('quote.handover', ['quote' => 'quote_id']) }}";
                var modifiedRoute = route.replace("quote_id", quoteId);

                // Add quote id to FormData
                var formData = new FormData();
                // Add method PATCH to FormData
                formData.append('_method', 'PATCH');

                $.ajax({
                    url: modifiedRoute,
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if (response.success) {
                            // Show the success toast
                            $('#success-toast-message').text('Quotation has been successfully handovered.');
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();    // This show the toast
                            // Redirect to the quotation list page
                            setTimeout(function() {
                                window.location.href = "{{ route('quote.index') }}";
                            }, 1500);
                        }
                    },
                    error: function(xhr) {
                        console.log(xhr.responseJSON);
                        // Show the toast
                        $('#failed-toast-message').html(`Failed to handover quote due to ${xhr.responseJSON.error}`);
                        var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                        var toast = new bootstrap.Toast(toastElement, {
                            autohide: true,     // Automatically hide after the delay
                            delay: 3000         // Time in milliseconds before the toast hides
                        });
                        toast.show(); 

                        // Enable the button and change the text back to "Generate Quotation"
                        $('#btn-handover-quote').removeClass('disabled').text('Handover');
                    }
                });
            });

            // Return Quotation
            $('#btn-return-quote').on('click', function() {
                // Disable the button and change the text to "Please wait..."
                $('#btn-return-quote').text('Returning...').addClass('disabled');
                var quoteId = $('#quote_id').val();
                var route = "{{ route('quote.return', ['quote' => 'quote_id']) }}";
                var modifiedRoute = route.replace("quote_id", quoteId);

                // Add quote id to FormData
                var formData = new FormData();
                formData.append('return_remark', $('#return_remark').val());
                // Add method PATCH to FormData
                formData.append('_method', 'PATCH');

                $.ajax({
                    url: modifiedRoute,
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if (response.success) {
                            // Show the success toast
                            $('#success-toast-message').text('Quotation has been successfully returned.');
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();    // This show the toast
                            // Redirect to the quotation list page
                            setTimeout(function() {
                                window.location.href = "{{ route('quote.index') }}";
                            }, 1500);
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            var fileSection = ['loa', 'businessCase', 'proposal', 'arrangement', 'partner'];
                            // create error message
                            $.each(errors, function(key, value) {
                                var section = fileSection.find(section => key.startsWith(section));
                                if (section) {    // Check if the error is from file section
                                    showError(section, value.join(', '));
                                    console.log('loop1'+section);
                                } else {
                                    // Check if key contains '.'
                                    // if (key.includes('.')) {
                                    //     key = key.split('.')[0];
                                    // }
                                    var errorElement = $('#' + key + '_error');
                                    if (errorElement.length) {
                                        errorElement.text(value[0]).show();
                                        // Focus on the field with the error
                                        $('#' + key).focus();
                                    } else {
                                        errorHtml += '<li>' + value[0] + '</li>';
                                    }
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html(`Failed to return quote due to ${xhr.responseJSON.message}`);
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }

                        // Enable the button and change the text back to "Generate Quotation"
                        $('#btn-return-quote').removeClass('disabled').text('Return');
                    }

            });
                
        });


    });
    
    
    </script>
@endsection
