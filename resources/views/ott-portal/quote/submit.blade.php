@extends('layouts.master')
@section('title')
    Submit Quote
@endsection
@section('css')
    <!-- DataTables -->
    <link href="{{ URL::asset('/assets/libs/datatables/datatables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/dropzone/dropzone.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ URL::asset('/assets/libs/datepicker/datepicker.min.css') }}">
@endsection

@section('content')
    {{-- Customise issue on table element top border color --}}
    <style>
        #package-summary td {
            border-top: 0.1rem #eee solid !important;
        }
        /* Remove border, background, and outline for the input */
        .plain-input {
            border: none;
            background: transparent;
            box-shadow: none;
        }

        /* Remove the outline on focus */
        .plain-input:focus {
            outline: none;
            box-shadow: none;
        }

        /* Remove the hover effect */
        .plain-input:hover {
            border: none;
        }
        .selected-file {
            background-color: #FCF0DB;
            padding: 0.4rem 0.6rem;
            padding-right: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 999px;
            position: relative;
            display: inline-block;
            max-width: 100%;
            font-size: 0.7rem;
            color: #F1B44C;
            font-weight: 600
        }

        .remove-file {
            position: absolute;
            top: 50%;
            right: 1px;
            transform: translateY(-50%);
            color: #666;
            cursor: pointer;
            font-weight: normal;
            font-size: 16px;
            padding: 0 3px;
            opacity: 0.6;
            /* border: 1px solid #666; */
        }

        .remove-file:hover {
            opacity: 1;
        }

        .selected-files {
            max-height: 150px;
            overflow-y: auto;
            padding: 0.25rem;
        }

        .file-info {
            padding-right: 24px;
        }

        .file-name {
            font-size: 0.875rem;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            line-height: 1.2;
        }

        .file-size {
            font-size: 0.75rem;
            color: #666;
            margin-top: 2px;
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
        }

        td {
            vertical-align: top;
            padding: 1rem !important;
        }
    </style>
    @component('common-components.breadcrumb')
        @slot('pagetitle') Order Management @endslot
        @slot('title') Submit Quote @endslot
    @endcomponent
{{-- <ul><li>Generate Quote not working</li></ul> --}}
<div class="row">
    <div class="col-xl-12">
        <div class="custom-accordion">
            <div class="card mb-2">
                <a href="#quote-customerinfo-collapse" class="text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-receipt text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        01
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mt-2">Customer Details</h5>
                                {{-- <p class="text-muted text-truncate mb-0">Sed ut perspiciatis unde omnis iste</p> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <div id="quote-customerinfo-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row d-none">
                                    <label for="quote_id" class="col-md-4 col-form-label">Quote Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Quote Id." id="quote_id" name="quote_id" value="{{ $quote->id ?? '' }}">
                                        <small id="quote_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="customer_name" class="col-md-4 col-form-label">Customer Name</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Customer Name" id="customer_name" name="customer_name" value="{{ $quote->customer_name ?? '' }}" disabled>
                                        <small id="customer_name_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sfdc_id" class="col-md-4 col-form-label">SFDC Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="SFDC Id." id="sfdc_id" name="sfdc_id" value="{{ $quote->sfdc_id ?? '' }}" disabled>
                                        <small id="sfdc_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="brn" class="col-md-4 col-form-label">BRN</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="BRN" id="brn" name="brn" value="{{ $quote->brn ?? '' }}" disabled>
                                        <small id="brn_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="vertical" class="col-md-4 col-form-label">Vertical</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="vertical" name="vertical" disabled>
                                                    <option value="">Select Vertical...</option>
                                                    @foreach(\App\Models\Vertical::all() as $vertical)
                                                        <option value="{{ $vertical->name }}" {{ (isset($quote->vertical->name) && $quote->vertical->name == $vertical->name) ? 'selected' : '' }}>
                                                            {{ $vertical->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="vertical_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="vertical_others" name="vertical_others" value="{{ $quote->vertical_others ?? '' }}" disabled>
                                                <small id="vertical_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sale_segment" class="col-md-4 col-form-label">Sale Segment</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="sale_segment" name="sale_segment" disabled>
                                                    <option value="">Select Sale Segment...</option>
                                                    @foreach(\App\Models\SaleSegment::all() as $saleSegment)
                                                        <option value="{{ $saleSegment->name }}" {{ (isset($quote->sale_segment) && $quote->sale_segment->name == $saleSegment->name) ? 'selected' : '' }}>
                                                            {{ $saleSegment->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="sale_segment_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="sale_segment_others" name="sale_segment_others" value="{{ $quote->sale_segment_others ?? '' }}" disabled>
                                                <small id="sale_segment_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row">
                                    <label for="person_in_charge" class="col-md-4 col-form-label">Person In Charge</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Person In Charge" id="person_in_charge" name="person_in_charge" value="{{ $quote->person_in_charge ?? '' }}" disabled>
                                        <small id="person_in_charge_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="position_title" class="col-md-4 col-form-label">Position Title</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Position Title" id="position_title" name="position_title" value="{{ $quote->position_title ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="department" class="col-md-4 col-form-label">Department</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Department" id="department" name="department" value="{{ $quote->department ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contact_no" class="col-md-4 col-form-label">Telephone No.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Telephone No." id="contact_no" name="contact_no" value="{{ $quote->contact_no ?? '' }}" disabled>
                                        <small id="contact_no_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-center d-none">
                                    <label for="prepared_by" class="col-md-4 col-form-label">Prepared By</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Prepared By" id="prepared_by" name="prepared_by" value="{{ Str::ucfirst(optional(Auth::user())->name ?? '') }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="date" class="col-md-4 col-form-label">Date</label>
                                    <div class="col-md-8">
                                        <div class="input-group" id="datepicker">
                                            <input type="text" class="form-control" placeholder="Date" data-date-format="dd-mm-yyyy" data-date-container='#datepicker' data-provide="datepicker" name="date" id="date" value="{{ Carbon\Carbon::parse($quote->created_at)->format('d-m-Y') ?? '' }}" disabled>
                                            <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                        </div><!-- input-group -->
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <div class="mb-0 mb-md-1 row">
                                    <label for="unit_street" class="col-md-2 col-form-label">Address</label>
                                    <div class="col-md-5 px-md-1">
                                        <input class="form-control" type="text" placeholder="Unit No, Street Name" id="unit_street" name="unit_street" value="{{ $quote->address->unit_street ?? '' }}" disabled>
                                        <small id="unit_street_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-5 ps-md-0">
                                        <input class="form-control" type="text" placeholder="Housing Area / Development Name" id="housing_area" name="housing_area" value="{{ $quote->address->housing_area ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="postcode" class="col-md-2 col-form-label d-none d-md-block"></label>
                                    <div class="col-md-3 px-md-1">
                                        <input class="form-control" type="number" placeholder="Postcode" id="postcode" name="postcode" value="{{ $quote->address->postcode ?? '' }}" disabled>
                                        <small id="postcode_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-3 ps-md-0 pe-md-1">
                                        <input class="form-control" type="text" placeholder="City" id="city" name="city" value="{{ $quote->address->city ?? '' }}" disabled>
                                        <small id="city_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-4 ps-md-0">
                                        <input class="form-control" type="text" placeholder="State" id="state" name="state" value="{{ $quote->address->state ?? '' }}" disabled>
                                        <small id="state_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-package-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-truck text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        02
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Package</h5>
                                {{-- <p class="text-muted text-truncate mb-0">Neque porro quisquam est</p> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-package-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contract_length" class="col-md-4 col-form-label">Contract (Months)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Contract in months" id="contract_length" name="contract_length" min="1" step="1" value="{{ $quote->contract_length ?? '' }}" disabled>
                                        <small id="contract_length_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="col-12">
                                <div class="px-md-4 pb-4">
                                    <h5 class="font-size-14 my-3">Package Summary</h5>
                                    <p id="packages_error" class="text-danger error-message mb-1" style="display: none"></p>
                                    <table id="package-summary" class="table table-bordered nowrap" style="font-size: 0.8rem; border-collapse: collapse; border-spacing: 0; width: 100%;">
                                        <thead>
                                            <tr>
                                                <th>Table Id.</th>
                                                <th>Type</th>
                                                <th>Package</th>
                                                <th>Provider</th>
                                                <th>No. of User</th>
                                                <th>Price Id.</th>
                                                <th>Price (RM)</th>
                                                <th>Discount (%)</th>
                                                <th>Net Price (RM)</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be dynamically inserted here -->
                                        </tbody>
                                    </table>
                                    <div class="row justify-content-md-end">
                                        <div class="col-md-5 px-4">                        
                                            <h5 class="font-size-14 mb-3 mt-4">Price Summary</h5>
                                            <div class="mb-0 row">
                                                <label for="price_per_user" class="col-form-label col-6">Price (per user):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="price_per_user" name="price_per_user">
                                                </div>
                                            </div>
                                            <div class="mb-0 row">
                                                <label for="price_monthly" class="col-form-label col-6">Total Price (Monthly):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="price_monthly" name="price_monthly">
                                                </div>
                                            </div>
                                            <div class="mb-0 pb-2 row border-bottom">
                                                <label for="total_discount" class="col-form-label col-6">Total Discount (Monthly):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_discount" name="total_discount">
                                                </div>
                                            </div>
                                            {{-- <div class="mb-2 row border-bottom">
                                                <label for="discount_tier" class="col-form-label col-6">Discount Tier:</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="Discount Tier" id="discount_tier" name="discount_tier">
                                                </div>
                                            </div> --}}
                                            <div class="mb-0 mt-2 row">
                                                <label for="total_contract" class="col-form-label col-6">Subtotal Price (Contract):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_contract" name="total_contract">
                                                </div>
                                            </div>
                                            <div class="mb-0 pb-2 row border-bottom">
                                                <label for="tax" class="col-form-label col-6">Applicable Tax (8%):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="tax" name="tax">
                                                </div>
                                            </div>
                                            <div class="mb-0 mt-2 row">
                                                <label for="total_price" class="col-form-label col-6">Total Price (inclusive tax):</label>
                                                <div class="col-6">
                                                    <input class="form-control plain-input text-end" type="text" placeholder="RM 0.00" id="total_price" name="total_price">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>  
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-discount-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        03
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mt-2">Discount Tier</h5>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-discount-collapse" class="collapse">
                    <div class="px-4 pb-3">
                        {{-- <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-1 row">
                                    <label for="package_discount" class="col-md-4 col-form-label">Package Discount (%)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Package Discount (%)" id="package_discount" name="package_discount" min="0" max="100" value="{{ $quote->package_discount * 100 ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-1 row">
                                    <label for="subpackage_discount" class="col-md-4 col-form-label">Subpackage Discount (%)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="number" placeholder="Subpackage Discount (%)" id="subpackage_discount" name="subpackage_discount" min="0" max="100" value="{{ $quote->subpackage_discount * 100 ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <hr class="hr-dashed hr-menu">
                            </div>
                        </div> --}}
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row d-none">
                                    <label for="tier" class="col-md-4 col-form-label">Discount Tier Id</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Discount Tier Id" id="discount_tier_id" name="discount_tier_id" value="{{ $quote->discount_tier->id ?? '' }}" disabled>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="tier" class="col-md-4 col-form-label">Tier</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Tier" id="tier" name="tier" value="{{ $quote->discount_tier->tier_name ?? '' }}" disabled>
                                        <small id="tier_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="customer_name" class="col-md-4 col-form-label">Level Authority</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Level Authority" id="level_authority" name="level_authority" value="{{ $quote->discount_tier->authority_level ?? '' }}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row {{ $quote->attachments->isEmpty() ? 'd-none' : '' }}">
                                    <label for="tier" class="col-md-4 col-form-label">Supporting Document</label>
                                    <div class="col-md-8">
                                        {{-- Add a link to download existing attachment --}}
                                        @foreach ($quote->attachments as $attachment)
                                            @if($attachment->description == 'Discount Supporting Document')
                                                <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" class="btn btn-sm btn-outline-secondary mt-1" target="_blank">
                                                    {{ Str::limit($attachment->filename, 20) }} <i class="bx bx-download font-size-16"></i>
                                                </a>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-recipient-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        04
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Email Recipient List</h5>
                                {{-- <small class="text-muted text-truncate"><i>Review and select staff name list to get email notifications (allow multiple selections)</i></small> --}}
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-recipient-collapse" class="collapse">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="staff" class="col-md-3 col-form-label">*List of Staff (AM/Sales)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple" name="staff[]" id="staff" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%" disabled>
                                            @foreach (json_decode($quote->recipient_email) as $staff)
                                                <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endforeach
                                        </select>
                                        <small id="recipient_email_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-handover-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        05
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Handover Documents</h5>
                                <small class="text-muted text-truncate"><i> Attach all required (*) documents (allow multiple documents upload but total size should not exceed 2MB)</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-handover-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12 px-4">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 5%">Checklist</th>
                                            <th style="width: 40%">Documents</th>
                                            <th style="width: 55%">Selected Files</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Official LOA/PO - COMMENTED OUT FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" id="loaCheckbox" data-section="loa" disabled>
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">*Official LOA/PO</label>
                                                <input type="file" class="form-control file-input" id="loaFile" data-section="loa" multiple>
                                                <div class="error-message-file text-danger mt-1" id="loaError"></div>
                                            </td>
                                            <td>
                                                <div id="loaSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr> -->

                                        <!-- Business Case - COMMENTED OUT FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="businessCaseCheckbox" data-section="businessCase">
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">*Business Case/Discount Approval/Financial Analysis</label>
                                                <input type="file" class="form-control file-input" id="businessCaseFile" data-section="businessCase" multiple>
                                                <div class="error-message-file text-danger mt-1" id="businessCaseError"></div>
                                            </td>
                                            <td>
                                                <div id="businessCaseSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr> -->

                                        <!-- Final Proposal -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="proposalCheckbox" data-section="proposal">
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">*Final Proposal/Quotation submitted to customer</label>
                                                <input type="file" class="form-control file-input" id="proposalFile" data-section="proposal" multiple accept=".pdf">
                                                <div class="error-message-file text-danger mt-1" id="proposalError"></div>
                                            </td>
                                            <td>
                                                <div id="proposalSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr>

                                        <!-- Business Arrangement - COMMENTED OUT FOR FUTURE MODIFICATIONS -->
                                        <!-- <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="arrangementCheckbox" data-section="arrangement">
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">*Business Arrangement Form</label>
                                                <input type="file" class="form-control file-input" id="arrangementFile" data-section="arrangement" multiple>
                                                <div class="error-message-file text-danger mt-1" id="arrangementError"></div>
                                            </td>
                                            <td>
                                                <div id="arrangementSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr> -->

                                        <!-- PO/LOA to partner -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="partnerCheckbox" data-section="partner">
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">*PO/LOA to partner</label>
                                                <input type="file" class="form-control file-input" id="partnerFile" data-section="partner" multiple accept=".pdf">
                                                <div class="error-message-file text-danger mt-1" id="partnerError"></div>
                                            </td>
                                            <td>
                                                <div id="partnerSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr>

                                        <!-- Other Document (Optional) -->
                                        <tr>
                                            <td class="text-center align-middle">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input section-checkbox" type="checkbox" disabled id="otherDocumentCheckbox" data-section="otherDocument">
                                                </div>
                                            </td>
                                            <td class="px-2 px-md-3">
                                                <label class="form-label fw-bold">Other Document</label>
                                                <input type="file" class="form-control file-input" id="otherDocumentFile" data-section="otherDocument" multiple accept=".pdf">
                                                <div class="error-message-file text-danger mt-1" id="otherDocumentError"></div>
                                            </td>
                                            <td>
                                                <div id="otherDocumentSelectedFiles" class="selected-files"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#quote-team-member-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        06
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-0">Personal In Charge</h5>
                                <small class="text-muted text-truncate"><i>Select staff name to get email notifications (allow multiple selections)</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-team-member-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sd" class="col-md-3 col-form-label">*List of Staff (SD)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="sd[]" id="sd" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        </select>
                                        <small id="sd_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="pm" class="col-md-3 col-form-label">*List of Staff (PM)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="pm[]" id="pm" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        </select>
                                        <small id="pm_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="tad" class="col-md-3 col-form-label">*List of Staff (TAD)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="tad[]" id="tad" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        </select>
                                        <small id="tad_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sc" class="col-md-3 col-form-label">*List of Staff (SC)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="sc[]" id="sc" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        </select>
                                        <small id="sc_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="sc" class="col-md-3 col-form-label">*List of Staff (CM)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="cm[]" id="cm" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        </select>
                                        <small id="cm_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="row my-4">
            <div class="col">
                <div class="text-end mt-2 mt-sm-0">
                    <btn class="btn btn-info" id="btn-generate-quote">Generate Quotation</btn>
                    <btn class="btn btn-primary" id="btn-submit-quote">Submit for Verification</btn>
                    <btn class="btn btn-light" id="btn-back">Back</btn>
                </div>
            </div> <!-- end col -->
        </div> <!-- end row-->

        {{-- Toast --}}
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="align-items-center text-white bg-success border-0">
                    <div class="d-flex">
                        <div class="toast-body" id="success-toast-message">
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="align-items-center text-white bg-danger border-0">
                    <div class="d-flex">
                        <div class="toast-body" id="failed-toast-message">
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- end row -->

@endsection
@section('script')
    <script src="{{ URL::asset('/assets/libs/datatables/datatables.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/jszip/jszip.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/pdfmake/pdfmake.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/datatables.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/select2/select2.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/dropzone/dropzone.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/ecommerce-add-product.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/datepicker/datepicker.min.js') }}"></script>    

    <script>
        $(document).ready(function() {

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Click 'Back' button
            $('#btn-back').click(function() {
                window.location.href = "{{ route('quote.index') }}";
            });

            // Testing Purpose
            // $.ajax({
            //     url: "{{ route('postcode', ['postcode' => '26500']) }}",
            //     method: 'GET',
            //     success: function(data) {
            //         // Handle the response data
            //         console.log(data);
            //         // You can update the DOM with the fetched data here
            //     }
            // })

            // initialize the table
            var table = $('#package-summary').DataTable({
                paging: false,    // Disable paging
                ordering: false,  // Disable sorting
                searching: false,  // Disable searching
                info: false,      // Disable info
                scrollX: true,
                processing: true,
                columns: [
                    { data: "table_id", visible: false },  // Hide column
                    { data: "type", visible: false },  // Hide column
                    { data: "package" },                    
                    { data: "provider" },                    
                    { data: "no_user" },   
                    { data: "price_id", visible: false },  // Hide columns             
                    { data: "price" },                    
                    { data: "discount" },                    
                    { data: "net_price" },                    
                    { data: "action" },                    
                ]
            });

            // Auto-populate quote id
            var quote = @json($quote);
            $('.page-title').append(' [' + quote.quote_id + ']');

            // Auto-populate the package/subpackage table
            var quoteItem = @json($quote->quote_items ?? []);
            // console.log(quoteItem);

            quoteItem.forEach(function(item) {

                var data = {
                    table_id: item.quotable_id,
                    type: item.quotable_type.split('\\').pop().toLowerCase(),
                    package: item.description,
                    provider: item.provider,
                    no_user: item.quantity ?? '',
                    price_id: item.price.id,
                    price: item.discount_percentage > 0 ? (item.quoted_price / (1 - item.discount_percentage)).toFixed(2) : item.quoted_price, 
                    discount: item.discount_percentage*100,
                    net_price: item.quoted_price,
                    // action: item.quotable_type.includes('Package') ? "<a href='javascript:void(0);' name='btn-delete' class='px-2 text-secondary'><i class='uil uil-trash-alt font-size-14'></i></a>" : ""
                    action: ""
                }
                
                table.row.add(data).draw();
            });

            // adjust table design
            $('a[href="#quote-summary-collapse"], a[href="#quote-package-collapse"]').on('click', function() {
                table.columns.adjust().draw();
            });

            // Count Price Summary
            function calculatePriceSummary() {
                // console.log('Recalculating price summary...');
                let pricePerUser = 0;
                let subTotalPrice = 0;
                let totalDiscount = 0;

                // Iterate through all rows in the table
                table.rows().every(function() {

                    var row = this.node(); // Current row
                    var packageName = $(row).find('td:eq(0)').text().toLowerCase(); // Get package name from the first column

                    // Skip iteration if package name contains 'fixed' or 'mobile'
                    if (packageName.includes('fixed') || packageName.includes('mobile')) {
                        return;
                    }

                    var netPrice = parseFloat($(row).find('td:eq(3)').text()); // Get Net Price from the fifth column
                    var noUser = parseFloat($(row).find('td:eq(2)').text()); // Get No. of User from the third column
                    var discount = parseFloat($(row).find('td:eq(4)').text()) || 0; // Get Discount from the fourth column, assign to 0 if no value
                    var rowTotalDiscount = discount == 0 ? 0 : (netPrice * discount / 100 * noUser); // Assign 0 if no value

                    pricePerUser += netPrice;
                    subTotalPrice += netPrice * noUser;
                    totalDiscount += rowTotalDiscount;
                });

                $('#price_per_user').val(pricePerUser ? 'RM ' + pricePerUser.toFixed(2) : '');
                $('#price_monthly').val(subTotalPrice ? 'RM ' + subTotalPrice.toFixed(2) : '');
                $('#total_discount').val(totalDiscount ? 'RM ' + totalDiscount.toFixed(2) : '');
                $('#total_monthly').val(subTotalPrice || totalDiscount ? 'RM ' + (subTotalPrice - totalDiscount).toFixed(2) : '');
                $('#total_contract').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val()).toFixed(2) : '');
                $('#tax').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val() * 0.08).toFixed(2) : '');
                $('#total_price').val(subTotalPrice || totalDiscount ? 'RM ' + ((subTotalPrice - totalDiscount) * $("#contract_length").val() * 1.08).toFixed(2) : '');
            }

            // Recalculate when table data changes
            table.on('draw', calculatePriceSummary());

            // Disable the staff field
            $('#staff').addClass('disabled');

            // Initialize Select2 for Search
            $('#staff, #tad, #sc, #pm, #sd, #cm').select2({
                placeholder: "Search for a staff...",
                ajax: {
                    url: "{{ route('staff.search') }}",  // Your API route
                    dataType: 'json',
                    delay: 250,  // Delay for API calls to prevent too many requests
                    data: function(params) {
                        return {
                            info: params.term,  // The search term entered by the user
                        };
                    },
                    processResults: function(data) {
                        // Map the API response into the format that Select2 expects
                        return {
                            results: data.results.map(function(item) {
                                return {
                                    id: item.Name + ' (' + item.email + ')',  // Use Staff_No as the ID
                                    text: item.Name + ' (' + item.email + ')'    // Display "Staff_No - Name" as the text
                                };
                            })
                        };
                    },
                    cache: true
                },
                multiple: true,  // Allow multiple selections
                minimumInputLength: 2  // Start showing results after 2 characters
            });

            // $('#quote-recipient-collapse').on('shown.bs.collapse', function () {
            //     // After the collapsible section is shown, trigger the resize on Select2
            //     $('#staff').trigger('change');
            // });

            // Auto download PDF function
            function downloadPDF(url, filename) {
                // Create blob link to download
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', filename);
                link.setAttribute('target', '_blank');
                // Add link to body
                document.body.appendChild(link);
                // Start download
                link.click();
                // Clean up
                document.body.removeChild(link);
            }

            // Manage handover files
            const files = {
                loa: new Map(),
                businessCase: new Map(),
                proposal: new Map(),
                arrangement: new Map(),
                partner: new Map()
            };

            // Handle file selection
            $('.file-input').on('change', function(e) {
                const section = $(this).data('section');
                const fileList = e.target.files;
                
                for (let i = 0; i < fileList.length; i++) {
                    const file = fileList[i];
                    const fileId = Date.now() + '-' + i; // Unique identifier
                    files[section].set(fileId, file);
                }
                
                updateSelectedFiles(section);
                updateCheckbox(section);
            });

            // Update selected files display
            function updateSelectedFiles(section) {
                const container = $(`#${section}SelectedFiles`);
                container.empty();

                files[section].forEach((file, fileId) => {
                    const fileElement = $(`
                        <div class="selected-file" data-file-id="${fileId}">
                            <span>${file.name} (${formatFileSize(file.size)})</span>
                            <span class="remove-file" data-section="${section}" data-file-id="${fileId}">×</span>
                        </div>
                    `);
                    container.append(fileElement);
                });
            }

            // Format file size
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Update checkbox state
            function updateCheckbox(section) {
                const hasFiles = files[section].size > 0;
                $(`#${section}Checkbox`).prop('checked', hasFiles);
            }

            // Remove file
            $(document).on('click', '.remove-file', function() {
                const section = $(this).data('section');
                const fileId = $(this).data('file-id');
                
                files[section].delete(fileId);
                updateSelectedFiles(section);
                updateCheckbox(section);
                
                // Reset file input
                $(`#${section}File`).val('');
            });

            // Clear all error messages
            function clearErrors() {
                $('.error-message-file').empty();
                $('.error-message').hide();
                // $('.file-input').removeClass('is-invalid');
            }

            // Show error message for a specific section
            function showError(section, message) {
                $(`#${section}Error`).html(`<small>${message}</small>`);
                // $(`#${section}File`).addClass('is-invalid');
            }

            // Handle upload
            $('#btn-submit-quote').on('click', function() {

                // temporary disable the button and change the text
                $('#btn-submit-quote').text('Submitting...').addClass('disabled');

                // Clear previous error messages
                // clearErrors();
                
                // Check each section and show specific error messages
                // let hasErrors = false;
                // Object.keys(files).forEach(section => {
                //     if (files[section].size === 0) {
                //         showError(section, 'Please upload required document');
                //         hasErrors = true;
                //     }
                // });

                // Check select2 fields have values - using class name to target all select2 fields
                // $(".list_of_staff").each(function() {
                //     if ($(this).val() == '') {
                //         var id = $(this).attr('id');
                //         $(`#${id}_error`).text('Please select at least one staff').show();
                //         hasErrors = true;
                //     }
                // });

                // if (hasErrors) {
                //     // temporary disable the button and change the text
                //     $('#btn-submit-quote').text('Submit for Verification').removeClass('disabled');

                //     // Show the toast
                //     $('#failed-toast-message').html("Please fill in all required fields before submit the quote.");
                //     var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                //     var toast = new bootstrap.Toast(toastElement, {
                //         autohide: true,     // Automatically hide after the delay
                //         delay: 3000         // Time in milliseconds before the toast hides
                //     });
                //     toast.show(); 
                //     return;
                // }

                // Create FormData object
                const formData = new FormData();
                
                // Add files to FormData
                Object.keys(files).forEach(section => {
                    files[section].forEach((file, fileId) => {
                        formData.append(`${section}[]`, file);
                    });
                });

                // add select2 values to FormData
                const staffTypes = ['sd', 'pm', 'tad', 'sc', 'cm'];
                staffTypes.forEach(type => {
                    const selected = $(`#${type}`).val();
                    if (selected && selected.length > 0) {
                        selected.forEach(staffId => {
                            formData.append(`${type}[]`, staffId);
                        });
                    }
                });
                // recipient email
                var staffEmails = $('#staff').val();
                if (Array.isArray(staffEmails)) {
                    staffEmails.forEach(function(email, index) {
                        formData.append('recipient_email[' + index + ']', email);
                    });
                }

                // Add quote id to FormData
                formData.append('quote_id', $("#quote_id").val());
                // Add method PATCH to FormData
                formData.append('_method', 'PATCH');

                // Submit the form
                var route = "{{ route('quote.update.submit', ['quote' => ':id']) }}".replace(':id', $('#quote_id').val());
                $.ajax({
                    url: route,
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        console.log(response);
                        if (response.success) {
                            // Show the successful toast
                            $('#success-toast-message').text(response.message);
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();  // This show the toast
                            // Redirect to the quotation list page
                            setTimeout(function() {
                                window.location.href = "{{ route('quote.index') }}";
                            }, 1500);
                        }
                        else {
                            if (response.includes('Max size is 25MB')) {
                                // Show the toast
                                $('#failed-toast-message').html("Total attached file size cannot exceed 25MB.");
                                var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                                var toast = new bootstrap.Toast(toastElement, {
                                    autohide: true,     // Automatically hide after the delay
                                    delay: 3000         // Time in milliseconds before the toast hides
                                });
                                toast.show(); 
                            }
                        }
                        $('#btn-submit-quote').text('Submit for Verification').removeClass('disabled');
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            var fileSection = ['loa', 'businessCase', 'proposal', 'arrangement', 'partner'];
                            // create error message
                            $.each(errors, function(key, value) {
                                var section = fileSection.find(section => key.startsWith(section));
                                if (section) {    // Check if the error is from file section
                                    showError(section, value.join(', '));
                                    console.log('loop1'+section);
                                } else {
                                    // Check if key contains '.'
                                    // if (key.includes('.')) {
                                    //     key = key.split('.')[0];
                                    // }
                                    console.log('loop2'+key);
                                    var errorElement = $('#' + key + '_error');
                                    if (errorElement.length) {
                                        errorElement.text(value[0]).show();
                                        // Focus on the field with the error
                                        $('#' + key).focus();
                                    } else {
                                        errorHtml += '<li>' + value[0] + '</li>';
                                    }
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields and check files format.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html(`Failed to submit quote due to ${xhr.responseJSON.message}`);
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }
                        // enable the button and change the text
                        $('#btn-submit-quote').text('Submit for Verification').removeClass('disabled');
                    }
                });

            });

            // Generate Quotation
            $('#btn-generate-quote').on('click', function() {

                // Disable the button to prevent multiple clicks and change value to 'Generating...'
                $('#btn-generate-quote').addClass('disabled').text('Generating...');

                // Get customer details
                var dataCustomer = {
                    id: $('#quote_id').val(),
                    customer: $('#customer_name').val(),
                    address: $('#unit_street').val() ? `${$('#unit_street').val().replace(/,\s*$/, '')}, ${$('#housing_area').val().replace(/,\s*$/, '')}, ${$('#postcode').val()}, ${$('#city').val()}, ${$('#state').val()}` : '',
                    pic: $('#person_in_charge').val(),
                    title_department: $('#department').val() ? `${$('#position_title').val()}, ${$('#department').val()}` : $('#position_title').val(),
                    contact_no: $('#contact_no').val(),
                    sfdc_id: $('#sfdc_id').val(),
                    brn: $('#brn').val(),
                    date: $('#date').val(),
                    prepared_by: $('#prepared_by').val(),
                }

                // Check if all Customer details are filled
                var allFieldsFilled = Object.values(dataCustomer).every(function(value) {
                    return value !== '' && value !== null && value !== undefined;
                });

                if (!allFieldsFilled) {
                    // Show the toast
                    $('#failed-toast-message').text('Please fill in all customer details before generating the quotation.');
                    var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                    var toast = new bootstrap.Toast(toastElement, {
                        autohide: true,     // Automatically hide after the delay
                        delay: 3000         // Time in milliseconds before the toast hides
                    });
                    toast.show();  // This shows the toast

                    // Enable the button and change value back to 'Generate Quote'
                    $('#btn-generate-quote').removeClass('disabled').text('Generate Quote');

                    return;  // Exit the function if not all fields are filled
                }

                var dataOther = {
                    contract: `${$('#contract_length').val()} months`,
                    price_per_user: $('#price_per_user').val() ? parseFloat($('#price_per_user').val().replace('RM ', '')).toFixed(2) : null,
                    price_monthly: $('#price_monthly').val() ? parseFloat($('#price_monthly').val().replace('RM ', '')).toFixed(2) : null,
                    total_discount: $('#total_discount').val() ? parseFloat($('#total_discount').val().replace('RM ', '')).toFixed(2) : null,
                    total_contract: $('#total_contract').val() ? parseFloat($('#total_contract').val().replace('RM ', '')).toFixed(2) : null,
                    tax: $('#tax').val() ? parseFloat($('#tax').val().replace('RM ', '')).toFixed(2) : null,
                    total_price: $('#total_price').val() ? parseFloat($('#total_price').val().replace('RM ', '')).toFixed(2) : null,
                };

                // Combine all data into one object
                var requestData = {
                    ...dataCustomer,
                    ...dataOther,
                    datatableData: table.rows().data().toArray()
                };

                $.ajax({
                    url: "{{ route('quote.generate') }}",
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: requestData,
                    success: function(response) {
                        if (response.success) {
                            // console.log(response);
                            // Download PDF
                            downloadPDF(response.download_url, response.filename);

                            // Enable the button and change value back to 'Generate Quote'
                            $('#btn-generate-quote').removeClass('disabled').text('Generate Quote');
                            
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            $('#error-messages').removeClass('d-none').html(errorHtml);
                        } else {
                            $('#error-messages')
                                .removeClass('d-none')
                                .html('An error occurred while generating the PDF.');
                        }

                        // Enable the button and change value back to 'Generate Quote'
                        $('#btn-generate-quote').removeClass('disabled').text('Generate Quote');
                    },
                });
            });

            // Auto download PDF function
            function downloadPDF(url, filename) {
                // Create blob link to download
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', filename);
                link.setAttribute('target', '_blank');
                // Add link to body
                document.body.appendChild(link);
                // Start download
                link.click();
                // Clean up
                document.body.removeChild(link);
            }

        });
    
    
    </script>
@endsection
