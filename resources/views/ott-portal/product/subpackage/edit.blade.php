@extends('layouts.master')
@section('title')
    Edit Subpackage
@endsection
@section('css')
    <!-- Add any additional CSS here -->
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
    @component('common-components.breadcrumb')
        @slot('pagetitle') Product Management @endslot
        @slot('title') Edit Subpackage @endslot
    @endcomponent

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-md-5 px-4">

                    <h4 class="card-title mb-1">Edit Subpackage</h4>
                    <small class="card-title-desc text-muted">Fill in all required (*) fields and click 'Update' button</small>

                    <div class="mb-3 mt-4 row">
                        <label for="example-text-input" class="col-md-2 col-form-label">Provider</label>
                        <div class="col-md-10">
                            <input class="form-control" type="text" value="{{ $subpackage->package->provider->name ?? '' }}" id="provider" name="provider" disabled>
                        </div>
                    </div>
                    <div class="mb-3 mt-4 row">
                        <label for="example-text-input" class="col-md-2 col-form-label">Name</label>
                        <div class="col-md-10">
                            <input class="form-control" type="text" value="{{ $subpackage->name ?? '' }}" id="name" disabled>
                        </div>
                    </div>
                    {{-- <div class="mb-3 mt-4 row">
                        <label for="description" class="col-md-2 col-form-label">Description</label>
                        <div class="col-md-10">
                            <input class="form-control" type="text" value="{{ $subpackage->description ?? '' }}" id="description" name="description" disabled>
                        </div>
                    </div> --}}
                    <div class="mb-3 mt-4 row">
                        <label for="material_number" class="col-md-2 col-form-label">*Material Number</label>
                        <div class="col-md-10">
                            <input class="form-control" type="text" value="{{ $subpackage->material_number ?? '' }}" id="material_number" name="material_number">
                            <small class="text-danger error-message" style="display: none" id="material_number_error"></small>
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="subpackage_status" class="col-md-2 col-form-label">Status</label>
                        <div class="col-md-10">
                            <select class="form-select" id="subpackage_status" name="subpackage_status" disabled>
                                <option value="active" {{ $subpackage->status == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ $subpackage->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="fixed_to_fixed_price" class="col-md-2 col-form-label">*Fixed to Fixed Price (RM)</label>
                        <div class="col-md-10">
                            <input class="form-control" type="hidden" value="{{ $prices['fixed_to_fixed']['id'] ?? '' }}" id="fixed_to_fixed_price_id" name="fixed_to_fixed_price_id" disabled>
                            <input class="form-control" type="text" id="fixed_to_fixed_price" name="fixed_to_fixed_price" value="{{ number_format($prices['fixed_to_fixed']['amount'] ?? 0, 2) }}">
                            <small class="text-danger error-message" style="display: none" id="fixed_to_fixed_price_error"></small>
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label for="fixed_to_mobile_price" class="col-md-2 col-form-label">*Fixed to Mobile Price (RM)</label>
                        <div class="col-md-10">
                            <input class="form-control" type="hidden" value="{{ $prices['fixed_to_mobile']['id'] ?? '' }}" id="fixed_to_mobile_price_id" name="fixed_to_mobile_price_id" disabled>
                            <input class="form-control" type="text" id="fixed_to_mobile_price" name="fixed_to_mobile_price" value="{{ number_format($prices['fixed_to_mobile']['amount'] ?? 0, 2) }}">
                            <small class="text-danger error-message" style="display: none" id="fixed_to_mobile_price_error"></small>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Button --}}
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-secondary" id="btn-update-subpackage">Update</button>
                <button type="button" class="btn btn-light ms-2" onclick="window.history.back();" id="btn-back-subpackage">Back</button>
            </div>
            {{-- Toast Success and Error --}}
            <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
                <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="align-items-center text-white bg-success border-0">
                        <div class="d-flex">
                            <div class="toast-body" id="success-toast-message">
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
                <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="align-items-center text-white bg-danger border-0">
                        <div class="d-flex">
                            <div class="toast-body" id="failed-toast-message">
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@section('script')
    <script>
    $(document).ready(function() {
        // Enable CSRF token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Update button click event
        $('#btn-update-subpackage').click(function() {

            // Disable the button to prevent multiple clicks and show loading state
            $(this).addClass('disabled').text('Updating...');

            // Clear previous error messages
            $('.error-message').hide();
            $('.error-message').text('');

            // Get form data
            // Create form data object
            var formData = new FormData();
            formData.append('material_number', $('#material_number').val());
            formData.append('fixed_to_fixed_price', $('#fixed_to_fixed_price').val());
            formData.append('fixed_to_mobile_price', $('#fixed_to_mobile_price').val());
            formData.append('_method', 'PUT');
            formData.append('fixed_to_fixed_price_id', $('#fixed_to_fixed_price_id').val());
            formData.append('fixed_to_mobile_price_id', $('#fixed_to_mobile_price_id').val());

            // Send AJAX request to update subpackage
            $.ajax({
                url: "{{ route('subpackage.update', $subpackage->id) }}",
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    console.log(response);
                    if (response.success) {
                        // Show success toast
                        $('#success-toast-message').text(response.message);
                        $('#success-toast').toast('show');
                        // Redirect to subpackage index page after 2 seconds
                        setTimeout(function() {
                            window.location.href = "{{ route('subpackage.index', $subpackage->id) }}";
                        }, 2000);
                    } else {
                        // Show error toast
                        $('#failed-toast-message').text(response.message);
                        $('#failed-toast').toast('show');
                    }
                },
                error: function(xhr, status, error) {
                    // Handle validation errors
                    console.log(xhr);
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $.each(errors, function(key, value) {
                            $('#' + key + '_error').text(value[0]).show();
                        });
                        // Show error toast
                        $('#failed-toast-message').text(xhr.responseJSON.message);
                        $('#failed-toast').toast('show');
                    } else {
                        // Show error toast
                        $('#failed-toast-message').text('An error occurred while updating the subpackage.');
                        $('#failed-toast').toast('show');
                    }
                    // Re-enable the button and reset text
                    $('#btn-update-subpackage').removeClass('disabled').text('Update');
                },
                complete: function() {
                    // Re-enable the button and reset text
                    $('#btn-update-subpackage').removeClass('disabled').text('Update');
                }
            });
        })
    });
    </script>
@endsection