# SBC Termination Steps - All Packages
*Generated: 24 September 2025*

## Overview
This document outlines the main SBC termination steps for all package types based on code analysis. The steps are brief and capture only the main operations.

## Package Types
- **Teams**: Standard package, automated termination
- **Webex**: Standard package, automated termination, requires SIP Agent deletion
- **Zoom**: Standard package, automated termination, requires SIP Agent + SIP Interface + Steering Pool deletion
- **Others**: Manual package, requires manual activities

---

## Common Termination Flow (All Standard Packages)

### 1. Authentication
- Obtain Bearer access token from SBC API
- Validate token for subsequent API calls

### 2. Lock Configuration
- Lock SBC configuration for exclusive access
- Prevent concurrent modifications during termination

### 3. Backup Configuration
- Create safety backup of running configuration
- Generate timestamped backup file for rollback

### 4. Process Inbound LRT Termination
- Discover existing inbound routing table
- Download and backup current inbound file via SFTP
- Remove phone numbers for terminated services
- Identify realms safe for deletion (no remaining routes)
- Upload modified inbound routing table

### 5. Process Outbound LRT Termination
- Discover existing outbound routing table  
- Download and backup current outbound file via SFTP
- Remove phone numbers for terminated services
- Identify IMS realms safe for deletion (no remaining routes)
- Upload modified outbound routing table

---

## Package-Specific Termination Steps

### Teams Package
**Termination Actions:**
- **Route Removal**: Remove routes with egress=teams-{BRN}
- **Realm Deletion**: Delete teams-{BRN} realm if no active routes remain
- **Routing Group**: Processes TeamsGRP routing removal

### Webex Package
**Termination Actions:**
- **Route Removal**: Remove routes with egress=webex-{BRN}
- **SIP Agent Deletion**: Delete Webex SIP Agent configuration
- **Realm Deletion**: Delete webex-{BRN} realm if no active routes remain
- **Routing Group**: Processes WebexGRP routing removal

### Zoom Package
**Termination Actions:**
- **Route Removal**: Remove routes with egress=zoom-{BRN}
- **SIP Agent Deletion**: Delete dual SIP Agent configurations (hostname1 & hostname2)
- **SIP Interface Deletion**: Delete Zoom-specific SIP interface
- **Steering Pool Deletion**: Delete Zoom steering pool configuration
- **Realm Deletion**: Delete zoom-{BRN} realm if no active routes remain
- **Routing Group**: Processes ZoomGRP routing removal

### Others Package
**Processing:**
- **Manual Activity Creation**: Create manual termination activity
- **No Automated SBC**: Skips automated SBC termination workflow
- **Manual Intervention Required**: Requires staff to complete termination

---

## Conditional Deletion Steps

### 6. Delete Package Realms (Conditional)
- **Condition**: Only if no other services use the realm
- **Action**: Delete package-specific realms (teams-BRN, webex-BRN, zoom-BRN)
- **Validation**: Based on LRT processing results from steps 4-5

### 7. Delete IMS Realm (Conditional)  
- **Condition**: Only if no other services use tmims-{BRN}
- **Action**: Delete tenant IMS child realm
- **Validation**: Based on outbound LRT processing results

---

## Final Termination Steps

### 8. Save Configuration
- Persist all termination changes
- Prepare for verification

### 9. Verify Configuration
- Validate configuration integrity
- Check for configuration errors

### 10. Activate Configuration
- Apply all termination changes
- Make configuration active

### 11. Unlock Configuration
- Release configuration lock
- Allow other operations to proceed

---

## Termination Scenarios

### Rollback Scenario
- **Order Status**: 'completed rollback'
- **Phone Number Status**: 'available'
- **Service History**: 'SBC deactivated'
- **Trigger**: Order rollback request

### Full Termination Scenario  
- **Order Status**: 'cancelled'
- **Phone Number Status**: 'deactivated'
- **Service History**: 'SBC deactivated'
- **Trigger**: Order cancellation

---

## Error Handling
- **Any Step Failure**: Unlock configuration and terminate
- **Retry Logic**: Manual retry available through SBC Retry Controller
- **Logging**: Comprehensive logging throughout all steps
- **Configuration Safety**: Always unlock on exception

---

## Package Strategy Decision Matrix

| Package Combination | Strategy | SBC Processing | Manual Activities |
|---------------------|----------|----------------|-------------------|
| Teams/Webex/Zoom only | standard_only | Automated | None |
| Mixed (Standard + Others) | mixed_packages | Automated for Standard | Manual for Others |
| Others only | others_only | None | Manual for All |

---

## Post-Termination Actions
- **Service Deactivation**: Update service status based on scenario
- **Phone Number Status**: Update phone numbers based on scenario  
- **Service History**: Create termination history records
- **Activity Completion**: Complete any existing manual activities
- **FQDN/IP Cleanup**: Update FQDN, Service IP, and Public IP status
- **IME Rate Pricing**: Process cancellation if no active services remain
- **Audit Logging**: Log all termination activities

---

## SIP Agent Deletion Logic
- **Webex**: Single SIP Agent deletion
- **Zoom**: Dual SIP Agent deletion (hostname1 & hostname2)
- **Condition**: Only delete if package realm is being deleted
- **Safety Check**: Verify no active routes before deletion

---

*Note: This documentation is based on code analysis of the SBC termination system as of September 2025.*
