# SBC Provisioning Steps - All Packages
*Generated: 24 September 2025*

## Overview
This document outlines the main SBC provisioning steps for all package types based on code analysis. The steps are brief and capture only the main operations.

## Package Types
- **Teams**: Standard package, automated provisioning, requires FQDN
- **Webex**: Standard package, automated provisioning, requires SIP Agent + FQDN  
- **Zoom**: Standard package, automated provisioning, requires SIP Agent + SIP Interface + Steering Pool
- **Others**: Manual package, requires manual activities

---

## Common Provisioning Flow (All Standard Packages)

### 1. Authentication
- Obtain Bearer access token from SBC API
- Validate token for subsequent API calls

### 2. Lock Configuration  
- Lock SBC configuration for exclusive access
- Prevent concurrent modifications

### 3. Backup Configuration
- Create safety backup of running configuration
- Generate timestamped backup file

### 4. Check Package Realms
- Verify existence of package-specific realms
- Track realm existence status for optimization

### 5. Create Package Realms
- Create missing package-specific realms (teams-BRN, webex-BRN, zoom-BRN)
- Apply package-specific templates with BRN substitution

---

## Package-Specific Steps

### Teams Package
**Additional Steps:**
- **FQDN Template Replacement**: Replace {{FQDN}} placeholder in realm template
- **Routing Group**: Uses TeamsGRP for LRT routing

### Webex Package  
**Additional Steps:**
- **FQDN Template Replacement**: Replace {{FQDN}} placeholder in realm template
- **SIP Agent Creation**: Create Webex SIP Agent configuration
- **Routing Group**: Uses WebexGRP for LRT routing

### Zoom Package
**Additional Steps:**
- **SIP Agent Creation**: Create dual SIP Agent configurations (hostname1 & hostname2)
- **SIP Interface Creation**: Create Zoom-specific SIP interface
- **Steering Pool Creation**: Create Zoom steering pool configuration  
- **Routing Group**: Uses ZoomGRP for LRT routing

### Others Package
**Processing:**
- **Manual Activity Creation**: Create manual provisioning activity
- **No Automated SBC**: Skips automated SBC provisioning workflow
- **Manual Intervention Required**: Requires staff to complete provisioning

---

## Common Final Steps (All Standard Packages)

### 6. Check/Create IMS Realm
- Verify tenant IMS realm existence
- Calculate concurrent channels based on total phone numbers
- Create or update IMS realm with proper channel allocation

### 7. Process Inbound LRT
- Discover existing inbound routing table
- Download and backup current inbound file via SFTP
- Add package-specific phone number routes
- Upload modified inbound routing table

### 8. Process Outbound LRT  
- Discover existing outbound routing table
- Download and backup current outbound file via SFTP
- Add IMS-specific phone number routes
- Upload modified outbound routing table

### 9. Save Configuration
- Persist all configuration changes
- Prepare for verification

### 10. Verify Configuration
- Validate configuration integrity
- Check for configuration errors

### 11. Activate Configuration
- Apply all provisioning changes
- Make configuration active

### 12. Unlock Configuration
- Release configuration lock
- Allow other operations to proceed

---

## Error Handling
- **Any Step Failure**: Unlock configuration and terminate
- **Retry Logic**: Manual retry available through SBC Retry Controller
- **Logging**: Comprehensive logging throughout all steps
- **Rollback**: Configuration backup available for rollback scenarios

---

## Package Strategy Decision Matrix

| Package Combination | Strategy | SBC Processing | Manual Activities |
|---------------------|----------|----------------|-------------------|
| Teams/Webex/Zoom only | standard_only | Automated | None |
| Mixed (Standard + Others) | mixed_packages | Automated for Standard | Manual for Others |
| Others only | others_only | None | Manual for All |

---

## Post-Provisioning Actions
- **Service Activation**: Update service status to 'activated'
- **Phone Number Status**: Update phone numbers to 'in use'
- **Service History**: Create service history records
- **Activity Completion**: Complete any existing manual activities
- **Audit Logging**: Log all provisioning activities

---

*Note: This documentation is based on code analysis of the SBC provisioning system as of September 2025.*
