<?php

namespace App\Http\Controllers;

use App\Models\DiscountTier;
use App\Models\Provider;
use Illuminate\Http\Request;
use App\Models\Package;
use App\Models\Postcode;
use App\Models\Product;
use App\Services\InvokeEraApiService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\File;
use Mpdf\Mpdf;
use App\Http\Requests\CustomQuoteStoreRequest;
use App\Mail\CreateQuoteEmail;
use App\Mail\AcceptQuoteEmail;
use App\Mail\HandoverQuoteEmail;
use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Attachment;
use App\Models\Price;
use App\Models\Quote;
use App\Models\QuoteItem;
use App\Models\User;
use App\Services\AuditService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Exceptions\PostTooLargeException;
use Illuminate\Support\Facades\Storage;
use App\Models\SOF;
use App\Models\Vertical;
use App\Models\SaleSegment;



class QuotationController extends Controller
{
    protected $invokeEraApiService;

    // Inject the InvokeEraApiService into the controller
    public function __construct(InvokeEraApiService $invokeEraApiService)
    {
        $this->middleware('auth');
        $this->invokeEraApiService = $invokeEraApiService;
    }

    // Controller method to handle the profile search
    public function searchStaff(Request $request)
    {
        // Retrieve the 'info' parameter from the request
        $info = $request->input('info');

        // Call the service's getProfileSearch method
        $result = $this->invokeEraApiService->getProfileSearch($info);

        // Return the result as JSON (or you could pass it to a view if needed)
        return response()->json($result);
    }

    // format datetime
    public function reformatDateTime($dateTime) {
        // Convert the default timestamp format to the desired format
        return Carbon::parse($dateTime, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y H:i');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('ott-portal.quote.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('ott-portal.quote.create');
    }

    // Get list of packages
    public function getPackagesBasedOnProvider($providerId = null)
    {
        $currentDate = now();

        if ($providerId) {
            $data = Package::with([
            'product',
            // 'vertical',
            'provider',
            'subpackages',
            'prices' => function ($query) use ($currentDate) {
                $query->where('effective_from', '<=', $currentDate)
                  ->where(function ($query) use ($currentDate) {
                      $query->where('effective_to', '>=', $currentDate)
                        ->orWhereNull('effective_to');
                  });
            },
            // 'subpackages.prices' => function ($query) use ($currentDate) {
            //     $query->where('effective_from', '<=', $currentDate)
            //       ->where(function ($query) use ($currentDate) {
            //           $query->where('effective_to', '>=', $currentDate)
            //             ->orWhereNull('effective_to');
            //       });
            // }
            ])
            ->where('provider_id', $providerId)
            ->get()->toArray();
        }
        else {
            $data = Package::with([
            'product',
            // 'vertical',
            'provider',
            'prices' => function ($query) use ($currentDate) {
                $query->where('effective_from', '<=', $currentDate)
                  ->where(function ($query) use ($currentDate) {
                      $query->where('effective_to', '>=', $currentDate)
                        ->orWhereNull('effective_to');
                  });
            },
            'subpackages'
            // 'subpackages.prices' => function ($query) use ($currentDate) {
            //     $query->where('effective_from', '<=', $currentDate)
            //       ->where(function ($query) use ($currentDate) {
            //           $query->where('effective_to', '>=', $currentDate)
            //             ->orWhereNull('effective_to');
            //       });
            // }
            ])->get()->toArray();
        }

        // // Get global prices once instead of per subpackage
        $globalPrices = Price::where('entity_type', 'global')
            ->whereNull('effective_to')
            ->get()->toArray();

        foreach ($data as $key => $package) {
            // Add global prices to the package['subpackages'] array
            if (isset($data[$key]['subpackages'][0])) {
                $data[$key]['subpackages'][0]['prices'] = $globalPrices;
            }
        }

        // // Add global prices to the package['subpackages'] array
        // $data['subpackages'][0]['prices'] = $globalPrices;

        $packages = [];

        foreach ($data as $package) {
            // Ensure that the 'prices' array is not empty
            $price = isset($package['prices'][0]) ? (float)$package['prices'][0]['amount'] : 0.00;

            $packages[] = [
                'id' => $package['id'],
                'name' => $package['product']['name'] . ' - ' . $package['provider']['name'] . ' (' . ucfirst($package['name']) . ')',
                'price' => $price,
            ];
        }

        return response()->json(compact('packages'));
    }

    // Get package details
    public function getPackageDetails($packageId, $packageName = null)
    {
        $currentDate = now();

        // Fetch the package with its associated product, provider, and subpackages
        $package = Package::with([
            'provider',
            'prices' => function ($query) use ($currentDate) {
                $query->where('effective_from', '<=', $currentDate)
                      ->where(function ($query) use ($currentDate) {
                          $query->where('effective_to', '>=', $currentDate)
                                ->orWhereNull('effective_to');
                      });
            },
            'subpackages',
            // 'subpackages.prices' => function ($query) use ($currentDate) {
            //     $query->where('effective_from', '<=', $currentDate)
            //           ->where(function ($query) use ($currentDate) {
            //               $query->where('effective_to', '>=', $currentDate)
            //                     ->orWhereNull('effective_to');
            //           });
            // }
        ])->find($packageId)->toArray();

        // Get global prices once instead of per subpackage
        $globalPrices = Price::where('entity_type', 'global')
            ->whereNull('effective_to')
            ->get()->toArray();

        // Add global prices to the package['subpackages'] array
        if (isset($package['subpackages'][0])) {
            $package['subpackages'][0]['prices'] = $globalPrices;
        }

        // dd($package);

        // Transform the data and add global prices
        // $data = $data->map(function ($package) use ($globalPrices) {
        //     $package = $package->toArray();
        //     foreach ($package['subpackages'] as &$subpackage) {
        //         $subpackage['prices'] = [
        //             $globalPrices['fixed to fixed']->toArray(),
        //             $globalPrices['fixed to mobile']->toArray()
        //         ];
        //     }
        //     return $package;
        // })->toArray();

        $subpackages = [];
        if ($packageName) {
            foreach ($package['subpackages'] as $subpackage) {
                $price_fixed = null;
                $price_fixed_id = null;
                $price_mobile = null;
                $price_mobile_id = null;

                foreach ($subpackage['prices'] as $price) {
                    if ($price['description'] == 'fixed to fixed') {
                        $price_fixed = number_format((float)$price['amount'], 2, '.', '');
                        $price_fixed_id = $price['id'];
                    } elseif ($price['description'] == 'fixed to mobile') {
                        $price_mobile = number_format((float)$price['amount'], 2, '.', '');
                        $price_mobile_id = $price['id'];
                    }
                }

                $subpackages[] = [
                    'id' => $subpackage['id'],
                    'name' => str_replace('SBCaaS -', '', $packageName . ' ' . ucwords($subpackage['name'])),
                    'price_fixed' => $price_fixed,
                    'price_fixed_id' => $price_fixed_id,
                    'price_mobile' => $price_mobile,
                    'price_mobile_id' => $price_mobile_id
                ];
            }
        }

        return response()->json([
            'id' => $package['id'],
            'provider' => isset($package['provider']) ? $package['provider']['name'] : 'Unknown',
            'price' => isset($package['prices'][0]) ? number_format((float)$package['prices'][0]['amount'], 2, '.', '') : '0.00',
            'subpackages' => $subpackages,
            'package_price_id' => isset($package['prices'][0]) ? $package['prices'][0]['id'] : null
        ]);
    }

    // Get info from percentage
    public function getDiscountTier($discount)
    {
        $discount = (float)$discount;
        $discountTiers = DiscountTier::all();

        $tier = $discountTiers->filter(function ($tier) use ($discount) {
            return $discount >= $tier->discount_min && $discount <= $tier->discount_max;
        })->first();

        return response()->json(compact('tier'));
    }

    // Get City and State from Postcode
    public function getCityAndStateFromPostcode($postcode)
    {
        $info = Postcode::where('postcode', $postcode)
            ->with('state') // Assuming there is a relationship defined in the Postcode model for state
            ->first();
            // ->toArray();

        if (!$info) {
            return response()->json(['error' => 'Postcode not found'], 404);
        }

        return response()->json($info->toArray());
    }

    // Generate quote
    public function generateQuote(Request $request)
    {
        // Load a view to pass data to
        // $pdf = PDF::loadView('pdf.example', $data);
        // $pdf = Pdf::loadView('ott-portal.quote.generatePdf', ['quote' => $request->all()]);

        try {

            if (isset($request->id)) {
                $quote = Quote::findOrFail($request->id);
                $filename = 'Quote_' . $quote->quote_id . '.pdf';
            }
            else {
                $filename = 'Quote_' . time() . '.pdf';
            }

            $pdf = Pdf::loadView('ott-portal.quote.generatePdf', ['quote' => $request->all()])->setOptions(['isRemoteEnabled' => true]);

            // Generate a unique filename
            // $filename = 'quote_' . time() . '.pdf';
            
            // Ensure the temp directory exists
            $quotePath = storage_path('app/public/quote');
            if (!File::exists($quotePath)) {
                File::makeDirectory($quotePath, 0755, true);
            }
            
            // Save PDF temporarily
            $pdf->save($quotePath . '/' . $filename);

            // Log the activity for quote
            AuditService::logActivity(
                'generated',
                'App\Models\Quote',
                isset($quote) ? $quote->id : null,
                'Generated ' . $filename
            );

            return response()->json([
                'success' => true,
                'data' => $request->all(),
                'filename' => $filename,
                'download_url' => route('file.download', ['folder' => 'quote', 'filename' => $filename])
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);

        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CustomQuoteStoreRequest $request)
    {

        try {

            DB::beginTransaction(); // Start transaction

            $validatedData = $request->validated();

            $recipientEmails = [];
            foreach ($validatedData['recipient_email'] as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }
            
            // Store in Quote table
            $quote = Quote::create([
                'quote_id' => (string) Quote::generateUniqueQuoteId(),
                'customer_name' => $validatedData['customer_name'],
                'person_in_charge' => $validatedData['person_in_charge'],
                'position_title' => $validatedData['position_title'],
                'department' => $validatedData['department'],
                'contact_no' => $validatedData['contact_no'],
                'sfdc_id' => $validatedData['sfdc_id'],
                'brn' => $validatedData['brn'],
                'unit_street' => $validatedData['unit_street'],
                'housing_area' => $validatedData['housing_area'],
                'postcode' => $validatedData['postcode'],
                'city' => $validatedData['city'],
                'state' => $validatedData['state'],
                'contract_length' => $validatedData['contract_length'],
                // 'package_discount' => $validatedData['package_discount'] !== null ? $validatedData['package_discount'] / 100 : null,
                // 'subpackage_discount' => $validatedData['subpackage_discount'] !== null ? $validatedData['subpackage_discount'] / 100 : null,
                'discount_tier_id' => $validatedData['discount_tier_id'],
                'status' => 'in progress',
                'total_amount' => $validatedData['total_contract'],
                'total_discount' => $validatedData['total_discount'],
                'valid_until' => now()->addDays(30),
                'recipient_email' => json_encode($recipientEmails),
                'tax' => $validatedData['tax'],
                'vertical_id' => Vertical::where('name', $validatedData['vertical'])->value('id'),
                'vertical_others' => strtoupper($validatedData['vertical_others']),
                'sale_segment_id' => SaleSegment::where('name', $validatedData['sale_segment'])->value('id'),
                'sale_segment_others' => strtoupper($validatedData['sale_segment_others']),
            ]);

            // Store address
            $quote->address()->create(
                [
                    'unit_street' => $validatedData['unit_street'],
                    'housing_area' => $validatedData['housing_area'],
                    'postcode' => $validatedData['postcode'],
                    'city' => $validatedData['city'],
                    'state' => $validatedData['state']
                ]
            );

            // Store attachments
            $file = $request->file('supporting_document');
            if ($file) {
                // Check if file size is greater than 10MB
                if ($file->getSize() > 10485760) {
                    throw new \Exception('File size exceeds the maximum limit of 10MB.');
                }

                $filename = $file->getClientOriginalName();
                // Store file in storage/app/public/attachments
                $path = $file->storeAs('attachments', $filename, 'public');
                Attachment::create([
                    'attachable_id' => $quote->id,
                    'attachable_type' => 'App\Models\Quote',
                    'filename' => $filename,
                    'description' => "Discount Supporting Document",
                    'mime_type' => $file->getClientMimeType(),
                    'path' => $path,
                    'size' => $file->getSize()
                ]);
            }

            // Store in QuoteItem table
            foreach ($validatedData['packages'] as $package) {
                $quoteItem = QuoteItem::create([
                    'quote_id' => $quote->id,
                    'quotable_type' => 'App\Models\\' . ucfirst($package['type']),
                    'quotable_id' => $package['table_id'],
                    'quantity' => $package['no_user'],
                    'price_id' => $package['price_id'],
                    'quoted_price' => $package['net_price'],
                    'discount_percentage' => $package['discount'] / 100,
                    'discount_amount' => $package['price'] - $package['net_price'],
                    'total_amount' => $package['net_price'] * $package['no_user'],
                    'description' => $package['package'],
                    'provider' => $package['provider']
                ]);
            }

            // Generate Quote in PDF
            // Compile data for PDF
            $data = [
                'customer' => $request->customer_name,
                'pic' => $request->person_in_charge,
                'address' => $request->unit_street . ', ' . $request->housing_area . ', ' . $request->postcode . ' ' . $request->city . ', ' . $request->state,
                'title_department' => $request->position_title . ', ' . $request->department,
                'contact_no' => $request->contact_no,
                'sfdc_id' => $request->sfdc_id,
                'brn' => $request->brn,
                'date' => $request->date,
                'prepared_by' => $request->prepared_by,
                'contract' => $request->contract_length . ' months',
                'price_per_user' => $request->price_per_user,
                'price_monthly' => $request->price_monthly,
                'total_discount' => $request->total_discount,
                'total_contract' => $request->total_contract,
                'tax' => $request->tax,
                'total_price' => $request->total_price,
                'datatableData' => $request->packages,
            ];

            $pdf = Pdf::loadView('ott-portal.quote.generatePdf', ['quote' => $data])->setOptions(['isRemoteEnabled' => true]);
            $filename = 'Quote_' . $quote->quote_id . '.pdf';
            $quotePath = storage_path('app/public/quote');
            if (!File::exists($quotePath)) {
                File::makeDirectory($quotePath, 0755, true);
            }
            $pdf->save($quotePath . '/' . $filename);

            // Prepared data for email
            if (!empty($recipientEmails)) {
                $emailData = [
                    'process' => 'created',
                    'receiver' => $recipientEmails[0]['name'] ?? 'Unknown',
                    'quote_id' => $quote->quote_id,
                    'created_at' => $this->reformatDateTime($quote->created_at),
                    'customer_name' => $quote->customer_name,
                    'address' => $quote->address->unit_street . ', ' . $quote->address->housing_area . ', ' . $quote->address->postcode . ' ' . $quote->address->city . ', ' . $quote->address->state,
                    'sfdc_id' => $quote->sfdc_id,
                    'valid_until' => Carbon::parse($quote->valid_until, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y'),
                    'view_url' => route('quote.view', ['quote' => $quote->id], true),
                ];

                // Send email to recipient
                $primaryEmail = $recipientEmails[0]['email'] ?? null;
                if ($primaryEmail) {
                    $ccEmails = count($recipientEmails) > 1 ? array_column(array_slice($recipientEmails, 1), 'email') : [];
                    Mail::to($primaryEmail)
                        ->cc($ccEmails)
                        ->send(new CreateQuoteEmail($emailData, $quotePath . '/' . $filename));
                }
            }

            DB::commit(); // Commit transaction

            // Log the activity for quote
            AuditService::logActivity(
                'created',
                get_class($quote),
                $quote->id,
                'Quote ' . $quote->quote_id . ' is created'
            );

            // Log the activity for each quote item
            foreach ($quote->quote_items as $quoteItem) {
                AuditService::logActivity(
                    'created',
                    get_class($quoteItem),
                    $quoteItem->id,
                    'Quote item ' . $quoteItem->description . ' is created'
                );
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Quote ' . $quote->quote_id . ' is created'
            ]);
        }
        catch (\Exception $e) {

            DB::rollBack(); // Rollback transaction

            return response()->json([
                'success' => false,
                'message' => 'Error creating quote',
                'error' => $e->getMessage(),
                // 'recipient_email' => $recipientEmails
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function saveAsDraft(Request $request)
    {

        try {

            DB::beginTransaction(); // Start transaction

            // Transform the email recipient data to json
            $recipientEmails = [];
            foreach ($request->recipient_email as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }

            // check if quote id is provided, proceed with update, else create new quote
            if ($request->quote_id) {
                // dd($request->all());
                $quote = Quote::where('id', $request->quote_id)->first();
                $quote->update([
                    'customer_name' => $request->customer_name,
                    'vertical_id' => Vertical::where('name', $request->vertical)->value('id'),
                    'vertical_others' => strtoupper($request->vertical_others),
                    'sale_segment_id' => SaleSegment::where('name', $request->sale_segment)->value('id'),
                    'sale_segment_others' => strtoupper($request->sale_segment_others),
                    'person_in_charge' => $request->person_in_charge,
                    'position_title' => $request->position_title,
                    'department' => $request->department,
                    'contact_no' => $request->contact_no,
                    'sfdc_id' => $request->sfdc_id,
                    'brn' => $request->brn,
                    'unit_street' => $request->unit_street,
                    'housing_area' => $request->housing_area,
                    'postcode' => $request->postcode,
                    'city' => $request->city,
                    'state' => $request->state,
                    'contract_length' => $request->contract_length,
                    'package_discount' => $request->package_discount !== null ? $request->package_discount / 100 : null,
                    'subpackage_discount' => $request->subpackage_discount !== null ? $request->subpackage_discount / 100 : null,
                    'discount_tier_id' => $request->discount_tier_id,
                    'status' => 'draft',
                    'total_amount' => $request->total_contract != null ? $request->total_contract : null,
                    'total_discount' => $request->total_discount != null ? $request->total_discount : null,
                    // 'valid_until' => now()->addDays(30),
                    'recipient_email' => json_encode($recipientEmails),
                    'tax' => $request->tax !== null ? $request->tax : null,
                ]);

                // Update address
                $quote->address()->update([
                    'unit_street' => $request->unit_street,
                    'housing_area' => $request->housing_area,
                    'postcode' => $request->postcode,
                    'city' => $request->city,
                    'state' => $request->state,
                ]);

                // Store attachments
                $file = $request->file('supporting_document');
                if ($file) {
                    // Check if file size is greater than 10MB
                    if ($file->getSize() > 10485760) {
                        throw new \Exception('File size exceeds the maximum limit of 10MB.');
                    }

                    // Delete existing attachment from storage public/attachments
                    $existingAttachment = Attachment::where('attachable_id', $request->quote_id)->where('attachable_type', 'App\Models\Quote')->first();
                    if ($existingAttachment) {

                        // Delete existing attachment from Database
                        $existingAttachment->delete();

                        $existingAttachmentPath = storage_path('app/public/' . $existingAttachment->path);
                        if (File::exists($existingAttachmentPath)) {
                            File::delete($existingAttachmentPath);
                        }
                    }

                    $filename = $file->getClientOriginalName();
                    // Store file in storage/app/public/attachments
                    $path = $file->storeAs('attachments', $filename, 'public');
                    Attachment::create([
                        'attachable_id' => $quote->id,
                        'attachable_type' => 'App\Models\Quote',
                        'filename' => $filename,
                        'description' => "Discount Supporting Document",
                        'mime_type' => $file->getClientMimeType(),
                        'path' => $path,
                        'size' => $file->getSize()
                    ]);
                }

                // Before creating new quote items, delete existing quote items
                QuoteItem::where('quote_id', $quote->id)->delete();

                // Store in QuoteItem table
                $quoteItems = [];
                if (isset($request->packages)) {
                    foreach ($request->packages as $package) {
                        $quoteItems[] = QuoteItem::create([
                            'quote_id' => $quote->id,
                            'quotable_type' => 'App\Models\\' . ucfirst($package['type']),
                            'quotable_id' => $package['table_id'],
                            'quantity' => $package['no_user'],
                            'price_id' => $package['price_id'],
                            'quoted_price' => $package['net_price'],
                            'discount_percentage' => $package['discount'] / 100,
                            'discount_amount' => $package['price'] - $package['net_price'],
                            'total_amount' => (float)$package['net_price'] * (integer)$package['no_user'],
                            'description' => $package['package'],
                            'provider' => $package['provider']
                        ]); 
                    }
                }

                DB::commit(); // Commit transaction

                // Log the activity for quote
                AuditService::logActivity(
                    'updated',
                    get_class($quote),
                    $quote->id,
                    'Quote (' . $quote->quote_id . ') updated as draft'
                );

                // Log the activity for each quote item
                if (!empty($quoteItems)) {
                    foreach ($quoteItems as $quoteItem) {
                        AuditService::logActivity(
                            'created',
                            get_class($quoteItem),
                            $quoteItem->id,
                            'Quote item ' . $quoteItem->description . ' saved as draft'
                        );
                    }
                };

            }
            else {

                // Store in Quote table
                $quote = Quote::create([
                    'quote_id' => (string) Quote::generateUniqueQuoteId(),
                    'customer_name' => $request->customer_name,
                    'person_in_charge' => $request->person_in_charge,
                    'position_title' => $request->position_title,
                    'department' => $request->department,
                    'contact_no' => $request->contact_no,
                    'sfdc_id' => $request->sfdc_id,
                    'brn' => $request->brn,
                    'unit_street' => $request->unit_street,
                    'housing_area' => $request->housing_area,
                    'postcode' => $request->postcode,
                    'city' => $request->city,
                    'state' => $request->state,
                    'contract_length' => $request->contract_length,
                    'package_discount' => $request->package_discount !== null ? $request->package_discount / 100 : null,
                    'subpackage_discount' => $request->subpackage_discount !== null ? $request->subpackage_discount / 100 : null,
                    'discount_tier_id' => $request->discount_tier_id,
                    'status' => 'draft',
                    'total_amount' => $request->total_contract != null ? $request->total_contract : null,
                    'total_discount' => $request->total_discount != null ? $request->total_discount : null,
                    // 'valid_until' => now()->addDays(30),
                    'recipient_email' => json_encode($recipientEmails),
                    'tax' => $request->tax !== null ? $request->tax : null,
                    'vertical_id' => Vertical::where('name', $request->vertical)->value('id'),
                    'vertical_others' => strtoupper($request->vertical_others),
                    'sale_segment_id' => SaleSegment::where('name', $request->sale_segment)->value('id'),
                    'sale_segment_others' => strtoupper($request->sale_segment_others),
                ]);

                // Store address
                $quote->address()->create(
                    [
                        'unit_street' => $request->unit_street,
                        'housing_area' => $request->housing_area,
                        'postcode' => $request->postcode,
                        'city' => $request->city,
                        'state' => $request->state
                    ]
                );

                // Store attachments
                $file = $request->file('supporting_document');
                if ($file) {
                    // Check if file size is greater than 10MB
                    if ($file->getSize() > 10485760) {
                        throw new \Exception('File size exceeds the maximum limit of 10MB.');
                    }

                    $filename = $file->getClientOriginalName();
                    // Store file in storage/app/public/attachments
                    $path = $file->storeAs('attachments', $filename, 'public');
                    Attachment::create([
                        'attachable_id' => $quote->id,
                        'attachable_type' => 'App\Models\Quote',
                        'filename' => $filename,
                        'description' => "Discount Supporting Document",
                        'mime_type' => $file->getClientMimeType(),
                        'path' => $path,
                        'size' => $file->getSize()
                    ]);
                }

                // Store in QuoteItem table 
                $quoteItems = [];
                if (isset($request->packages)) {
                    foreach ($request->packages as $package) {
                        $quoteItems[] = QuoteItem::create([
                            'quote_id' => $quote->id,
                            'quotable_type' => 'App\Models\\' . ucfirst($package['type']),
                            'quotable_id' => $package['table_id'],
                            'quantity' => $package['no_user'],
                            'price_id' => $package['price_id'],
                            'quoted_price' => $package['net_price'],
                            'discount_percentage' => $package['discount'] / 100,
                            'discount_amount' => $package['price'] - $package['net_price'],
                            'total_amount' => $package['net_price'] * $package['no_user'],
                            'description' => $package['package'],
                            'provider' => $package['provider']
                        ]);
                    }
                }

                DB::commit(); // Commit transaction

                // Log the activity for quote
                AuditService::logActivity(
                    'created',
                    get_class($quote),
                    $quote->id,
                    'Quote ' . $quote->quote_id . ' saved as draft'
                );

                // Log the activity for each quote item
                if (!empty($quoteItems)) {
                    foreach ($quoteItems as $quoteItem) {
                        AuditService::logActivity(
                            'created',
                            get_class($quoteItem),
                            $quoteItem->id,
                            'Quote item ' . $quoteItem->description . ' saved as draft'
                        );
                    }
                };

            }

            return response()->json([
                'success' => true,
                'message' => 'Quote is saved as draft',
                'data' => $request->all(),
                'test_data' => $quoteItems
            ]);


        }
        catch (\Exception $e) {

            DB::rollBack(); // Rollback transaction

            return response()->json([
                'success' => false,
                'message' => 'Error saving as draft',
                'error' => $e->getMessage(),
                'recipient_email' => $recipientEmails,
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($quote)
    {

        // return response()->json([
        //     'role'  => Auth::user()->getRoleNames()[0],
        // ]);

        try {

            // If admin/superadmin show all user quotes
            $userRoles = Auth::user()->getRoleNames();
            $userRole = $userRoles->isNotEmpty() ? $userRoles[0] : null;

            if ( $userRole === "admin" || $userRole === "superadmin" ) {
                if ($quote == 'all') {
                    $quotes = Quote::with([
                        'creator', 
                        'updater', 
                        'quote_items.quotable', 
                        'quote_items.price', 
                        'discount_tier',
                        'address',
                        'vertical',
                        'sale_segment'])->get()->toArray();
                } else {
                    $quotes = Quote::with([
                        'creator', 
                        'updater', 
                        'quote_items.quotable', 
                        'quote_items.price', 
                        'discount_tier',
                        'address',
                        'vertical',
                        'sale_segment'
                        ])->where('quote_id', $quote)
                        ->get()
                        ->toArray();
                }
            }
            else {
                if ($quote == 'all') {
                    $quotes = Quote::with([
                        'creator', 
                        'updater', 
                        'quote_items.quotable', 
                        'quote_items.price', 
                        'discount_tier',
                        'address',
                        'vertical',
                        'sale_segment'])
                        ->where('created_by', Auth::id())
                        ->orWhere(function ($query) {
                            $query->whereJsonContains('recipient_email', [['name' => Auth::user()->name]]);
                        })
                        ->get()->toArray();
                } else {
                    $quotes = Quote::with([
                        'creator', 
                        'updater', 
                        'quote_items.quotable', 
                        'quote_items.price', 
                        'discount_tier',
                        'address',
                        'vertical',
                        'sale_segment'])
                        ->where('quote_id', $quote)
                        ->where('created_by', Auth::id())
                        ->orWhere(function ($query) {
                            $query->whereJsonContains('recipient_email', [['name' => Auth::user()->name]]);
                        })
                        ->get()->toArray();
                }
            }

            $data = [];

            if (!empty($quotes)) {
                foreach ($quotes as $quote) {
                    $package = '';
                    foreach ($quote['quote_items'] as $quoteItem) {
                        if ($quoteItem['quotable_type'] == 'App\Models\Package') {
                            $package .= $quoteItem['description'] . '<br>';
                        }
                    }
                    // Create action buttons based on quote status
                    // Draft - Edit, View, Delete
                    // In Progress - Edit, View, Download PDF, Duplicate, Delete
                    // Expired, Rejected - View, Download PDF, Delete, Duplicate
                    // Accepted - View, Download PDF, Delete, Duplicate
                    if ($quote['status'] == 'draft') {
                        $action = "
                        <a href='" . route('quote.edit', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>
                        <a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        ";
                    } 
                    else if ($quote['status'] == 'rejected' || $quote['status'] == 'expired') {
                        $action = "
                        <a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('file.download', ['folder' => 'quote', 'filename' => 'Quote_' . $quote['quote_id'] . '.pdf']) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Download PDF'><i class='bx bxs-file-pdf font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-duplicate' id='duplicate_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate'><i class='bx bx-copy font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        " ;
                    } 
                    else if ($quote['status'] == 'in progress' && $quote['return_remark'] != null) {
                        $action = "
                        <a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('file.download', ['folder' => 'quote', 'filename' => 'Quote_' . $quote['quote_id'] . '.pdf']) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Download PDF'><i class='bx bxs-file-pdf font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-duplicate' id='duplicate_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate'><i class='bx bx-copy font-size-20'></i></a>
                        <a href='" . route('quote.reject', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Reject'><i class='bx bx-x-circle font-size-20'></i></a>
                        <a href='" . route('quote.resubmit', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Resubmit'><i class='bx bx-mail-send font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        " ;
                    }
                    else if ($quote['status'] == 'in progress') {
                        $action = "
                        <a href='" . route('quote.edit', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>
                        <a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('file.download', ['folder' => 'quote', 'filename' => 'Quote_' . $quote['quote_id'] . '.pdf']) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Download PDF'><i class='bx bxs-file-pdf font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-duplicate' id='duplicate_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate'><i class='bx bx-copy font-size-20'></i></a>
                        <a href='" . route('quote.reject', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Reject'><i class='bx bx-x-circle font-size-20'></i></a>
                        <a href='" . route('quote.submit', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Submit'><i class='bx bx-mail-send font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        " ;
                    } 
                    else if ($quote['status'] == 'pending verification') {
                        $action = 
                        // <a href='" . route('quote.edit', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>
                        "<a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('file.download', ['folder' => 'quote', 'filename' => 'Quote_' . $quote['quote_id'] . '.pdf']) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Download PDF'><i class='bx bxs-file-pdf font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-duplicate' id='duplicate_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate'><i class='bx bx-copy font-size-20'></i></a>
                        <a href='" . route('quote.verify', ['quote' => $quote['id']]) . "' name='btn-verify' id='verify_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Verify'><i class='bx bx-check-circle font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        " ;
                    }
                    else {
                        // Handover
                        $action = "
                        <a href='" . route('quote.view', ['quote' => $quote['id']]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('file.download', ['folder' => 'quote', 'filename' => 'Quote_' . $quote['quote_id'] . '.pdf']) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Download PDF'><i class='bx bxs-file-pdf font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-duplicate' id='duplicate_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate'><i class='bx bx-copy font-size-20'></i></a>
                        <a href='javascript:void(0);' name='btn-delete' id='delete_" . $quote['id'] . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Delete'><i class='bx bx-trash font-size-20'></i></a>
                        " ;
                    } 
                    $data[] = [
                        'quote_id' => $quote['quote_id'],
                        'customer_name' => $quote['customer_name'],
                        'brn' => $quote['brn'],
                        'sfdc_id' => $quote['sfdc_id'],
                        'vertical' => isset($quote['vertical']) ? ($quote['vertical']['name'] == 'OTHERS' ? $quote['vertical_others'] : $quote['vertical']['name']) : null,
                        'sale_segment' => isset($quote['sale_segment']) ? ($quote['sale_segment']['name'] == 'OTHERS' ? $quote['sale_segment_others'] : $quote['sale_segment']['name']) : null,
                        // 'package' => $quote['package'],
                        'package' => $package,
                        'contract_length' => $quote['contract_length'],
                        'status' => "<span class='badge " . 
                            ($quote['status'] == 'in progress' ? 'bg-warning-subtle text-warning' : 
                            ($quote['status'] == 'pending verification' ? 'bg-info-subtle text-info' : 
                            ($quote['status'] == 'draft' ? 'bg-dark-subtle' : 
                            (in_array($quote['status'], ['expired', 'rejected']) ? 'bg-danger-subtle text-danger' : 
                            ($quote['status'] == 'handover' ? 'bg-success-subtle text-success' : ''))))) . 
                            " font-size-12'>" . ucwords($quote['status']) . "</span>",
                        'valid_until' => $quote['valid_until'] ? Carbon::parse($quote['valid_until'], 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y') : null,
                        'created_by' => isset($quote['creator']) ? $quote['creator']['staff_id'] : 'N/A',
                        'updated_by' => isset($quote['updater']) ? $quote['updater']['staff_id'] : 'N/A',
                        'updated_at' => $this->reformatDateTime($quote['updated_at']),
                        'action' => $action

                    ];
                }
            }

            // Log the activity for quote
            // AuditService::logActivity(
            //     'viewed',
            //     'App\Models\Quote',
            //     null,
            //     'Viewed all quote details'
            // );

            return response()->json(compact('data'));
        } catch (\Exception $e) {
            return [
                'Error: ' . $e->getMessage(),
                'Line: ' . $e->getLine(),
                'File: ' . $e->getFile(),
            ];
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $quote = Quote::with([
            'creator', 
            'updater', 
            'quote_items.quotable', 
            'quote_items.price', 
            'discount_tier',
            'attachments',
            'vertical',
            'sale_segment',
            'address'
        ])->where('id', $id)->first();

        return view('ott-portal.quote.edit', compact('quote'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function view(string $id)
    {
        $quote = Quote::with([
            'creator', 
            'updater', 
            'quote_items.quotable', 
            'quote_items.price', 
            'discount_tier',
            'attachments',
            'vertical',
            'sale_segment',
            'address'
        ])->where('id', $id)->first();

        return view('ott-portal.quote.view', compact('quote'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function verify(string $id)
    {
        $quote = Quote::with([
            'creator', 
            'updater', 
            'quote_items.quotable', 
            'quote_items.price', 
            'discount_tier',
            'attachments',
            'vertical',
            'sale_segment',
            'address'
        ])->where('id', $id)->first();

        return view('ott-portal.quote.verify', compact('quote'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CustomQuoteStoreRequest $request, string $id)
    {
        try {

            DB::beginTransaction(); // Start transaction

            $validatedData = $request->validated();

            $recipientEmails = [];
            foreach ($validatedData['recipient_email'] as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }
            
            // Store in Quote table
            $quote = Quote::findOrFail($id);
            $quote->update([
                'customer_name' => $validatedData['customer_name'],
                'vertical' => $validatedData['vertical'],
                'person_in_charge' => $validatedData['person_in_charge'],
                'position_title' => $validatedData['position_title'],
                'department' => $validatedData['department'],
                'contact_no' => $validatedData['contact_no'],
                'sfdc_id' => $validatedData['sfdc_id'],
                'brn' => $validatedData['brn'],
                'unit_street' => $validatedData['unit_street'],
                'housing_area' => $validatedData['housing_area'],
                'postcode' => $validatedData['postcode'],
                'city' => $validatedData['city'],
                'state' => $validatedData['state'],
                'contract_length' => $validatedData['contract_length'],
                // 'package_discount' => $validatedData['package_discount'] !== null ? $validatedData['package_discount'] / 100 : null,
                // 'subpackage_discount' => $validatedData['subpackage_discount'] !== null ? $validatedData['subpackage_discount'] / 100 : null,
                'discount_tier_id' => $validatedData['discount_tier_id'],
                'status' => 'in progress',
                'total_amount' => $validatedData['total_contract'],
                'total_discount' => $validatedData['total_discount'],
                'valid_until' => now()->addDays(30),
                'recipient_email' => json_encode($recipientEmails),
                'tax' => $validatedData['tax'],
                'vertical_id' => Vertical::where('name', $validatedData['vertical'])->value('id'),
                'vertical_others' => strtoupper($validatedData['vertical_others']),
                'sale_segment_id' => SaleSegment::where('name', $validatedData['sale_segment'])->value('id'),
                'sale_segment_others' => strtoupper($validatedData['sale_segment_others']),
            ]);

            // Update address
            $quote->address()->update([
                'unit_street' => $validatedData['unit_street'],
                'housing_area' => $validatedData['housing_area'],
                'postcode' => $validatedData['postcode'],
                'city' => $validatedData['city'],
                'state' => $validatedData['state'],
            ]);

            // Replace attachments
            $file = $request->file('supporting_document');
            if ($file) {
                // Check if file size is greater than 10MB
                if ($file->getSize() > 10485760) {
                    throw new \Exception('File size exceeds the maximum limit of 10MB.');
                }

                // Delete existing attachment from storage public/attachments
                $existingAttachment = Attachment::where('attachable_id', $id)->where('attachable_type', 'App\Models\Quote')->first();
                if ($existingAttachment) {

                    // Delete existing attachment from Database
                    $existingAttachment->delete();

                    $existingAttachmentPath = storage_path('app/public/' . $existingAttachment->path);
                    if (File::exists($existingAttachmentPath)) {
                        File::delete($existingAttachmentPath);
                    }
                }

                $filename = $file->getClientOriginalName();                
                // Store file in storage/app/public/attachments
                $path = $file->storeAs('attachments', $filename, 'public');
                Attachment::create([
                    'attachable_id' => $quote->id,
                    'attachable_type' => 'App\Models\Quote',
                    'filename' => $filename,
                    'description' => "Discount Supporting Document",
                    'mime_type' => $file->getClientMimeType(),
                    'path' => $path,
                    'size' => $file->getSize()
                ]);
            }

            // Before creating new quote items, delete existing quote items
            QuoteItem::where('quote_id', $id)->delete();

            // Store in QuoteItem table
            foreach ($validatedData['packages'] as $package) {
                $quoteItem = QuoteItem::create([
                    'quote_id' => $quote->id,
                    'quotable_type' => 'App\Models\\' . ucfirst($package['type']),
                    'quotable_id' => $package['table_id'],
                    'quantity' => $package['no_user'],
                    'price_id' => $package['price_id'],
                    'quoted_price' => $package['net_price'],
                    'discount_percentage' => $package['discount'] / 100,
                    'discount_amount' => $package['price'] - $package['net_price'],
                    'total_amount' => $package['net_price'] * $package['no_user'],
                    'description' => $package['package'],
                    'provider' => $package['provider']
                ]);
            }

            // Commit transaction
            DB::commit();

            // Log the activity for quote
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote ' . $quote->quote_id . ' is updated'
            );

            // Log the activity for each quote item
            foreach ($quote->quote_items as $quoteItem) {
                AuditService::logActivity(
                    'created',
                    get_class($quoteItem),
                    $quoteItem->id,
                    'Quote item ' . $quoteItem->description . ' is updated'
                );
            }

            // Generate Quote in PDF
            // Compile data for PDF
            $data = [
                'customer' => $request->customer_name,
                'pic' => $request->person_in_charge,
                'address' => $request->unit_street . ', ' . $request->housing_area . ', ' . $request->postcode . ' ' . $request->city . ', ' . $request->state,
                'title_department' => $request->position_title . ', ' . $request->department,
                'contact_no' => $request->contact_no,
                'sfdc_id' => $request->sfdc_id,
                'brn' => $request->brn,
                'date' => $request->date,
                'prepared_by' => $request->prepared_by,
                'contract' => $request->contract_length . ' months',
                'price_per_user' => $request->price_per_user,
                'price_monthly' => $request->price_monthly,
                'total_discount' => $request->total_discount,
                'total_contract' => $request->total_contract,
                'tax' => $request->tax,
                'total_price' => $request->total_price,
                'datatableData' => $request->packages,
            ];
            $pdf = Pdf::loadView('ott-portal.quote.generatePdf', ['quote' => $data])->setOptions(['isRemoteEnabled' => true]);
            $filename = 'Quote_' . $quote->quote_id . '.pdf';
            $quotePath = storage_path('app/public/quote');
            if (!File::exists($quotePath)) {
                File::makeDirectory($quotePath, 0755, true);
            }
            $pdf->save($quotePath . '/' . $filename);

            // Prepared data for email
            if (!empty($recipientEmails)) {
                $emailData = [
                    'process' => $request->process,
                    'receiver' => $recipientEmails[0]['name'] ?? 'Unknown',
                    'quote_id' => $quote->quote_id,
                    'created_at' => $this->reformatDateTime($quote->created_at),
                    'customer_name' => $quote->customer_name,
                    'address' => $quote->address->unit_street . ', ' . $quote->address->housing_area . ', ' . $quote->address->postcode . ' ' . $quote->address->city . ', ' . $quote->address->state,
                    'sfdc_id' => $quote->sfdc_id,
                    'valid_until' => Carbon::parse($quote->valid_until, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y'),
                    'view_url' => route('quote.view', ['quote' => $quote->id], true),
                ];

                // Send email to recipient
                $primaryEmail = $recipientEmails[0]['email'] ?? null;
                if ($primaryEmail) {
                    $ccEmails = count($recipientEmails) > 1 ? array_column(array_slice($recipientEmails, 1), 'email') : [];
                    Mail::to($primaryEmail)
                        ->cc($ccEmails)
                        ->send(new CreateQuoteEmail($emailData, $quotePath . '/' . $filename));
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quote->quote_id . ') is processed successfully'
            ]);
        } catch (\Exception $e) {

            DB::rollBack(); // Rollback transaction

            return response()->json([
                'success' => false,
                'message' => 'Error editing quote',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Assign Permissions
        if (!auth()->user()->can('delete quote')) {
            abort(403, 'Unauthorized action.');
        }

        try {
            DB::beginTransaction(); // Start transaction

            // Retrieve the quote by its Id.
            $quote = Quote::findOrFail($id);

            // Get the quote id
            $quoteId = $quote->quote_id;

            // Get the quote items
            $quoteItems = QuoteItem::where('quote_id', $id)->get();

            // Delete the quote items
            foreach ($quoteItems as $quoteItem) {
                $quoteItem->delete();

                // Log the activity
                AuditService::logActivity(
                    'deleted',
                    get_class($quoteItem),
                    $quoteItem->id,
                    'Delete quote item (' . $quoteItem->description . ')'
                );
            }

            // Delete all the attachments
            $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', 'App\Models\Quote')->get();
            if ($attachments) {
                foreach ($attachments as $attachment) {
                    $attachment->delete();

                    // Delete the attachment from storage
                    $attachmentPath = storage_path('app/public/' . $attachment->path);
                    if (File::exists($attachmentPath)) {
                        File::delete($attachmentPath);
                    }
                }
            }

            // Delete quote PDF
            $quotePdf = storage_path('app/public/quote/Quote_' . $quoteId . '.pdf');
            if (File::exists($quotePdf)) {
                File::delete($quotePdf);
            }

            // Delete all related activities
            $activities = Activity::where('trackable_type', 'App\Models\Quote')->where('trackable_id', $id)->get();
            if ($activities) {
                foreach ($activities as $activity) {
                    $activity->delete();
                }
            }

            // Delete sof if exists
            $sof = $quote->sof;
            // dd($sof);
            if (isset($sof) && $sof) {
                $sof->delete();
            }

            // Delete activitiy related to sof
            if (isset($sof) && $sof) {
                $activities = Activity::where('trackable_type', 'App\Models\SOF')->where('trackable_id', $sof->id)->get();
                foreach ($activities as $activity) {
                    $activity->delete();
                }
            }

            // Delete address if exists
            $address = $quote->address;
            if (isset($address) && $address) {
                $address->delete();
            }

            // Delete the quote  
            $quote->delete();

            // Log the activity
            AuditService::logActivity(
                'deleted',
                get_class($quote),
                $quote->id,
                'Delete quote (' . $quoteId . ')'
            );

            DB::commit(); // Commit transaction

            // Redirect with a success message
            return redirect()->route('quote.index')->with('success', "Quote ($quoteId) was successfully deleted.");

        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return redirect()->back()->withErrors(['exception' => 'An error occurred: ' . $error->getMessage()])->withInput();
        }  
    }

    
    /**
     * Create duplicate function
     */
    public function duplicate(Quote $quote)
    {
        try {
            DB::beginTransaction();

            // Create new quote instance
            $newQuote = $quote->replicate();
            
            // Set new values as per requirements
            $newQuote->quote_id = Quote::generateUniqueQuoteId();
            $newQuote->status = 'draft';
            $newQuote->valid_until = null;
            $newQuote->rejection_reason = null;
            $newQuote->return_remark = null;
            $newQuote->project_team = null;
            $newQuote->recipient_email = json_encode([
                ['user_id' => auth()->user()->id, 'name' => auth()->user()->name, 'email' => auth()->user()->email]
            ]);
            $newQuote->created_by = auth()->user()->id;
            $newQuote->updated_by = auth()->user()->id;
            
            // Save the new quote
            $newQuote->save();

            // Duplicate QuoteItems
            foreach ($quote->quote_items as $item) {
                $newItem = $item->replicate();
                $newItem->quote_id = $newQuote->id;
                $newItem->save();

                // Log the activity for each duplicated quote item
                AuditService::logActivity(
                    'created',
                    get_class($newItem),
                    $newItem->id,
                    'Quote item ' . $newItem->description . ' is duplicated'
                );
            }

            // Duplicate Address
            $newAddress = $quote->address->replicate();
            $newAddress->addressable_id = $newQuote->id;
            $newAddress->save();

            // Log the activity for duplicated quote
            AuditService::logActivity(
                'duplicated',
                get_class($newQuote),
                $newQuote->id,
                'Quote ' . $newQuote->quote_id . ' is duplicated'
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $newQuote->quote_id . ') is duplicated successfully',
                'quote_id' => $newQuote->id // This will be used for the redirect
            ]);
        } 
        catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error duplicating quote',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function reject(string $id)
    {
        $quote = Quote::with([
            'creator', 
            'updater', 
            'quote_items.quotable', 
            'quote_items.price', 
            'discount_tier',
            'attachments'
        ])->where('id', $id)->first();

        return view('ott-portal.quote.reject', compact('quote'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function updateReject(Request $request, string $id)
    {

        // Validate the request
        $validator = Validator::make($request->all(), [
            'reject_reason' => 'required|string',
            'recipient_email' => 'required|array',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {

            $recipientEmails = [];
            foreach ($request->recipient_email as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }

            DB::beginTransaction(); // Start transaction

            $quote = Quote::where('id', $id)->first();
            $quote->status = 'rejected';
            $quote->updated_by = auth()->user()->id;
            $quote->rejection_reason = $request->reject_reason;
            $quote->recipient_email = json_encode($recipientEmails);
            $quote->save();

            // Log the activity
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote (' . $quote->quote_id . ') is rejected'
            );

            DB::commit(); // Commit transaction

            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quote->quote_id . ') is rejected successfully'
            ]);
        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure            
            
            return response()->json([
                'success' => false,
                'message' => 'Error rejecting quote',
                'error' => $error->getMessage()
            ], 500);
        }
    }

    /**
     * Display submit page.
     */
     public function submit(string $id)
     {
         $quote = Quote::with([
            'creator', 
            'updater', 
            'quote_items.quotable', 
            'quote_items.price', 
            'discount_tier',
            'attachments',
            'vertical',
            'sale_segment',
            'address'
         ])->where('id', $id)->first();
 
         return view('ott-portal.quote.submit', compact('quote'));
     }

    /**
     * Display resubmit page.
     */
     public function resubmit(string $id)
     {
         $quote = Quote::with([
             'creator', 
             'updater', 
             'quote_items.quotable', 
             'quote_items.price', 
             'discount_tier',
             'attachments',
             'vertical',
             'sale_segment',
             'address'
         ])->where('id', $id)->first();
 
         return view('ott-portal.quote.resubmit', compact('quote'));
     }

    /**
     * Update the specified resource in storage.
     */
    public function updateSubmit(Request $request, string $id){

        // Define validation rules for each section
        $rules = [
            'recipient_email' => ['required', 'array', 'min:1'],
            'sd' => ['required', 'array', 'min:1'],
            'pm' => ['required', 'array', 'min:1'],
            'tad' => ['required', 'array', 'min:1'],
            'sc' => ['required', 'array', 'min:1'],
            'cm' => ['required', 'array', 'min:1'],
            // Commented out for future modifications - these are no longer required
            // 'loa' => ['required', 'array', 'min:1'],
            // 'loa.*' => ['required', 'file', 'mimes:pdf', 'max:2048'],
            // 'businessCase' => ['required', 'array', 'min:1'],
            // 'businessCase.*' => ['required', 'file', 'mimes:pdf', 'max:2048'],
            // 'arrangement' => ['required', 'array', 'min:1'],
            // 'arrangement.*' => ['required', 'file', 'mimes:pdf', 'max:2048'],
            'proposal' => ['required', 'array', 'min:1'],
            'proposal.*' => ['required', 'file', 'mimes:pdf', 'max:2048'],
            'partner' => ['required', 'array', 'min:1'],
            'partner.*' => ['required', 'file', 'mimes:pdf', 'max:2048'],
            // New optional other document field
            'otherDocument' => ['nullable', 'array'],
            'otherDocument.*' => ['nullable', 'file', 'mimes:pdf', 'max:2048'],
        ];

        $messages = [
            // Staff validation messages
            'recipient_email.required' => 'Recipient email selection is required',
            'recipient_email.min' => 'Please select at least one recipient email',
            'sd.required' => 'SD staff selection is required',
            'sd.min' => 'Please select at least one SD staff member',
            'sd.*.exists' => 'Selected SD staff member is invalid',
            'pm.required' => 'PM staff selection is required',
            'pm.min' => 'Please select at least one PM staff member',
            'pm.*.exists' => 'Selected PM staff member is invalid',
            'tad.required' => 'TAD staff selection is required',
            'tad.min' => 'Please select at least one TAD staff member',
            'tad.*.exists' => 'Selected TAD staff member is invalid',
            'sc.required' => 'SC staff selection is required',
            'sc.min' => 'Please select at least one SC staff member',
            'sc.*.exists' => 'Selected SC staff member is invalid',
            'cm.required' => 'CM staff selection is required',
            'cm.min' => 'Please select at least one CM staff member',
            'cm.*.exists' => 'Selected CM staff member is invalid',
            // Document validation messages
            'proposal.required' => 'Final Proposal/Quotation document is required',
            'proposal.min' => 'At least one Final Proposal/Quotation file is required',
            'proposal.*.mimes' => 'Final Proposal/Quotation file must be a PDF',
            'proposal.*.max' => 'Final Proposal/Quotation file size should not exceed 2MB',
            'proposal.*.file' => 'The uploaded Final Proposal/Quotation file is invalid',
            'partner.required' => 'PO/LOA to partner document is required',
            'partner.min' => 'At least one PO/LOA to partner file is required',
            'partner.*.mimes' => 'PO/LOA to partner file must be a PDF',
            'partner.*.max' => 'PO/LOA to partner file size should not exceed 2MB',
            'partner.*.file' => 'The uploaded PO/LOA to partner file is invalid',
            'otherDocument.*.mimes' => 'Other document file must be a PDF',
            'otherDocument.*.max' => 'Other document file size should not exceed 2MB',
            'otherDocument.*.file' => 'The uploaded other document file is invalid'
        ];

        try {
            // Validate the request
            $validator = Validator::make($request->all(), $rules, $messages);

            // Check if validation fails
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation errors',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $recipientEmails = [];
            foreach ($request->recipient_email as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }

            // Compile all staff info
            $teamMembers = [];
            foreach ($request->sd as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'SD'
                    ];
                }
            }
            foreach ($request->sc as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'SC'
                    ];
                }
            }
            foreach ($request->pm as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'PM'
                    ];
                }
            }
            foreach ($request->tad as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'TAD'
                    ];
                }
            }
            foreach ($request->cm as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'CM'
                    ];
                }
            }

            DB::beginTransaction(); // Start transaction

            // Retrieve the quote by its Id.
            $quote = Quote::findOrFail($id);

            // Get the quote id
            $quoteId = $quote->quote_id;

            // Update the quote status
            $quote->status = 'pending verification';
            $quote->updated_by = auth()->user()->id;
            $quote->recipient_email = json_encode($recipientEmails);
            $quote->project_team = json_encode($teamMembers);
            $quote->save();

            // Upload all files
            $fileDescriptions = [
                // Commented out for future modifications
                // 'loa' => 'Official LOA/PO Document',
                // 'businessCase' => 'Business Case/Discount Approval Document',
                // 'arrangement' => 'Business Arrangement Form',
                'proposal' => 'Final Proposal/Quotation Document',
                'partner' => 'PO/LOA to Partner Document',
                'otherDocument' => 'Other Document'
            ];

            // Process files for each section (removed commented out sections)
            $sections = ['proposal', 'partner', 'otherDocument'];
            
            foreach ($sections as $section) {
                if ($request->hasFile($section)) {
                    foreach ($request->file($section) as $file) {
                        try {
                            // Check file size (10MB limit)
                            if ($file->getSize() > 10485760) {
                                throw new \Exception("File {$file->getClientOriginalName()} exceeds the maximum limit of 10MB.");
                            }

                            $filename = $file->getClientOriginalName();
                            
                            // Store file in appropriate directory
                            $path = $file->storeAs('attachments', $filename, 'public');

                            // Create attachment record
                            Attachment::create([
                                'attachable_id' => $quote->id,
                                'attachable_type' => Quote::class,
                                'filename' => $filename,
                                'description' => $fileDescriptions[$section],
                                'mime_type' => $file->getClientMimeType(),
                                'path' => $path,
                                'size' => $file->getSize()
                            ]);

                        } catch (\Exception $e) {
                            DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'message' => "Error processing file: {$file->getClientOriginalName()}",
                                'error' => $e->getMessage()
                            ], 500);
                        }
                    }
                }
            }

            // Get activity type
            $activityType = ActivityType::where('name', 'Verify Quote')->first();

            // Calculate planned_end_date considering only working days
            $plannedEndDate = Carbon::now();
            $daysToAdd = $activityType->duration;
            while ($daysToAdd > 0) {
                $plannedEndDate->addDay();
                if (!$plannedEndDate->isWeekend()) {
                    $daysToAdd--;
                }
            }

            // Create a new activity 'verify quote'
            $activity = Activity::create([
                'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                'activity_type_id' => $activityType->id,
                'trackable_id' => $quote->id,
                'trackable_type' => get_class($quote),
                'status' => 'in progress',
                'planned_start_date' => now(),
                'planned_end_date' => $plannedEndDate,
                'actual_start_date' => now(),
                'actual_end_date' => null,
                'actual_duration' => null,
                'aging' => null,
                'is_overdue' => false,
                'user_id' => null
            ]);

            // Create activity logs
            AuditService::logActivity(
                'generated',
                get_class($activity),
                $activity->id,
                'Activity ' . $activity->activityType->name . ' is created'
            );

            // Send Email to all team members
            $emailData = [
                // receiver is the team members with the role of SD
                // 'receiver' => collect($teamMembers)->where('role', 'SD')->pluck('email')->toArray(),
                // 'cc' => collect($teamMembers)->where('role', '!=', 'SD')->pluck('email')->toArray(),
                'quote_id' => $quote->quote_id,
                // 'created_at' => $this->reformatDateTime($quote->created_at),
                'customer_name' => $quote->customer_name,
                'address' => $quote->address->unit_street . ', ' . $quote->address->housing_area . ', ' . $quote->address->postcode . ' ' . $quote->address->city . ', ' . $quote->address->state,
                'sfdc_id' => $quote->sfdc_id,
                // 'valid_until' => Carbon::parse($quote->valid_until, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y'),
                'view_url' => route('quote.view', ['quote' => $quote->id], true),
                'process' => 'submit'
            ];

            // Send email to recipient
            Mail::to(collect($teamMembers)->where('role', 'SD')->pluck('email')->toArray())
                ->cc(array_merge(
                    collect($teamMembers)->where('role', '!=', 'SD')->pluck('email')->toArray(),
                    collect($recipientEmails)->pluck('email')->toArray()
                ))
                ->send(new AcceptQuoteEmail($emailData, storage_path('app/public/quote') . '/' . 'Quote_' . $quote->quote_id . '.pdf'));

            // Log the activity
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote ' . $quoteId . ' is submitted for verification'
            );

            DB::commit(); // Commit transaction

            // Redirect with a success message
            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quoteId . ') is successfully submitted for verification'
            ]);

        } catch (PostTooLargeException $e) {
            // Handle the specific PostTooLargeException
            return response()->json([
                'success' => false,
                'message' => 'The POST data is too large. Max size is 25MB.',
                'error' => $e->getMessage()
            ], 413); // 413 Payload Too Large
        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return response()->json([
                'success' => false,
                'message' => 'Error submitting quote',
                'error' => $error->getMessage(),
                // add error line and file
                'line' => $error->getLine(),
                'file' => $error->getFile(),
            ], 500);
        } 
    }

    /**
     * Handover Quote
     */

     public function handover($quote, Request $request)
     {
        try {

            DB::beginTransaction(); // Start transaction

            // Update the quote status
            $quote = Quote::findOrFail($quote);
            $quote->status = 'handover';
            $quote->updated_by = auth()->user()->id;
            $quote->save();

            // Update the activity status
            $activity = Activity::where('trackable_id', $quote->id)
                        ->where('trackable_type', get_class($quote))
                        ->whereHas('activityType', function ($query) {
                            $query->where('name', 'Verify Quote');
                        })->where('status', 'in progress')->first();
            $activity->status = 'Done';
            $activity->actual_end_date = now();
            $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                return !$date->isWeekend();
            }, $activity->actual_start_date);
            $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false; 
            $activity->user_id = auth()->user()->id;
            $activity->save();

            // Create a new activity 'Upload SOF'
            $activityType = ActivityType::where('name', 'Upload SOF')->first();
            $plannedEndDate = Carbon::now();
            $daysToAdd = $activityType->duration;
            while ($daysToAdd > 0) {
                $plannedEndDate->addDay();
                if (!$plannedEndDate->isWeekend()) {
                    $daysToAdd--;
                }
            }

            // Create a new SOF with remarks "Upload SOF" for the particular quote
            $sof = SOF::create([
                'sof_id' => SOF::generateUniqueSOFId(),
                'remark' => 'Upload SOF for Quote ' . $quote->quote_id,
                'quote_id' => $quote->id,
                'created_by' => auth()->user()->id,
                'updated_by' => auth()->user()->id,
            ]);

            $uploadSofActivity = Activity::create([
                'activity_id' => (string) Activity::generateUniqueActivityId(),
                'activity_type_id' => $activityType->id,
                'trackable_id' => $sof->id,
                'trackable_type' => get_class($sof),
                'status' => 'in progress',
                'planned_start_date' => now(),
                'planned_end_date' => $plannedEndDate,
                'actual_start_date' => now(),
                'actual_end_date' => null,
                'actual_duration' => null,
                'aging' => null,
                'is_overdue' => false,
                'user_id' => null,
                'updated_at' => now()->addMinutes(1)
            ]);

            DB::commit(); // Commit transaction

            // Prepared data for email
            $emailData = [
                'quote_id' => $quote->quote_id,
                'customer_name' => $quote->customer_name,
                'address' => $quote->address->unit_street . ', ' . $quote->address->housing_area . ', ' . $quote->address->postcode . ' ' . $quote->address->city . ', ' . $quote->address->state,
                'sfdc_id' => $quote->sfdc_id,
                'view_url' => route('quote.view', ['quote' => $quote->id], true),
            ];

            // Send email to recipient
            Mail::to(collect(json_decode($quote->project_team))->where('role', 'SD')->pluck('email')->toArray())
                ->cc(array_merge(
                    collect(json_decode($quote->project_team))->where('role', '!=', 'SD')->pluck('email')->toArray(),
                    collect(json_decode($quote->recipient_email))->pluck('email')->toArray()
                ))
                ->send(new HandoverQuoteEmail($emailData, storage_path('app/public/quote') . '/' . 'Quote_' . $quote->quote_id . '.pdf'));

            // Log the activity
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote ' . $quote->quote_id . ' is handovered'
            );

            // Send response as a success message
            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quote->quote_id . ') is successfully submitted for verification'
            ]);


        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return response()->json([
                'success' => false,
                'message' => 'Error handovering quote',
                'error' => $error->getMessage(),
                // add error line and file
                'line' => $error->getLine(),
                'file' => $error->getFile(),
            ], 500);
        } 
     }

    /**
     * Return Quote
     */

    public function return($quote, Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'return_remark' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            DB::beginTransaction(); // Start transaction

            // Update the quote status
            $quote = Quote::findOrFail($quote);
            $quote->return_remark = $quote->return_remark ? $quote->return_remark . "\n" . $request->return_remark : $request->return_remark;
            $quote->status = 'in progress';
            $quote->updated_by = auth()->user()->id;
            $quote->save();

            // Create a new activity 'Manage Return Quote'
            // Get activity type
            $activityType = ActivityType::where('name', 'Manage Return Quote')->first();

            // Calculate planned_end_date considering only working days
            $plannedEndDate = Carbon::now();
            $daysToAdd = $activityType->duration;
            while ($daysToAdd > 0) {
                $plannedEndDate->addDay();
                if (!$plannedEndDate->isWeekend()) {
                    $daysToAdd--;
                }
            }

            // Check if there is an existing 'Manage Return Quote' activity, only update the activity
            $activity = Activity::where('trackable_id', $quote->id)
                        ->where('trackable_type', get_class($quote))
                        ->where('activity_type_id', $activityType->id)->first();

            if ($activity) {
                // Update the activity status
                $activity->status = 'in progress';
                $activity->actual_end_date = null;
                $activity->actual_duration = null;
                $activity->is_overdue = false;
                $activity->save();
            }
            else {
                // Create a new activity 'Manage Return Quote'
                $activity = Activity::create([
                    'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                    'activity_type_id' => $activityType->id,
                    'trackable_id' => $quote->id,
                    'trackable_type' => get_class($quote),
                    'status' => 'in progress',
                    'planned_start_date' => now(),
                    'planned_end_date' => $plannedEndDate,
                    'actual_start_date' => now(),
                    'actual_end_date' => null,
                    'actual_duration' => null,
                    'aging' => null,
                    'is_overdue' => false,
                    'user_id' => null
                ]);

            }

            // Create activity logs
            AuditService::logActivity(
                'generated',
                get_class($activity),
                $activity->id,
                'Activity ' . $activity->activityType->name . ' is created'
            );

            DB::commit(); // Commit transaction

            // Log the activity
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote ' . $quote->quote_id . ' is returned'
            );

            // Success response
            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quote->quote_id . ') is successfully returned'
            ]);

        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return response()->json([
                'success' => false,
                'message' => 'Error returning quote',
                'error' => $error->getMessage(),
                // add error line and file
                'line' => $error->getLine(),
                'file' => $error->getFile(),
            ], 500);
        }

    }

    // Resubmit Quote
    public function updateResubmit(Request $request, $id) {
        // Define validation rules for each section
        $rules = [
            'recipient_email' => ['required', 'array', 'min:1'],
            'sd' => ['required', 'array', 'min:1'],
            'pm' => ['required', 'array', 'min:1'],
            'tad' => ['required', 'array', 'min:1'],
            'sc' => ['required', 'array', 'min:1'],
            'cm' => ['required', 'array', 'min:1'],
            // Commented out for future modifications - these are no longer required
            // 'loa' => ['array', 'min:1'],
            // 'loa.*' => ['file', 'mimes:pdf', 'max:2048'],
            // 'businessCase' => ['array', 'min:1'],
            // 'businessCase.*' => ['file', 'mimes:pdf', 'max:2048'],
            // 'arrangement' => ['array', 'min:1'],
            // 'arrangement.*' => ['file', 'mimes:pdf', 'max:2048'],
            'proposal' => ['array', 'min:1'],
            'proposal.*' => ['file', 'mimes:pdf', 'max:2048'],
            'partner' => ['array', 'min:1'],
            'partner.*' => ['file', 'mimes:pdf', 'max:2048'],
            // New optional other document field
            'otherDocument' => ['nullable', 'array'],
            'otherDocument.*' => ['nullable', 'file', 'mimes:pdf', 'max:2048'],
        ];

        $messages = [
            // Staff validation messages
            'recipient_email.required' => 'Recipient email selection is required',
            'recipient_email.min' => 'Please select at least one recipient email',
            'sd.required' => 'SD staff selection is required',
            'sd.min' => 'Please select at least one SD staff member',
            'pm.required' => 'PM staff selection is required',
            'pm.min' => 'Please select at least one PM staff member',
            'tad.required' => 'TAD staff selection is required',
            'tad.min' => 'Please select at least one TAD staff member',
            'sc.required' => 'SC staff selection is required',
            'sc.min' => 'Please select at least one SC staff member',
            'cm.required' => 'CM staff selection is required',
            'cm.min' => 'Please select at least one CM staff member',
            // Document validation messages
            'proposal.min' => 'At least one Final Proposal/Quotation file is required',
            'proposal.*.mimes' => 'Final Proposal/Quotation file must be a PDF',
            'proposal.*.max' => 'Final Proposal/Quotation file size should not exceed 2MB',
            'proposal.*.file' => 'The uploaded Final Proposal/Quotation file is invalid',
            'partner.min' => 'At least one PO/LOA to partner file is required',
            'partner.*.mimes' => 'PO/LOA to partner file must be a PDF',
            'partner.*.max' => 'PO/LOA to partner file size should not exceed 2MB',
            'partner.*.file' => 'The uploaded PO/LOA to partner file is invalid',
            'otherDocument.*.mimes' => 'Other document file must be a PDF',
            'otherDocument.*.max' => 'Other document file size should not exceed 2MB',
            'otherDocument.*.file' => 'The uploaded other document file is invalid'
        ];

        try {

            // Validate the request
            $validator = Validator::make($request->all(), $rules, $messages);

            // Check if validation fails
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation errors',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $recipientEmails = [];
            foreach ($request->recipient_email as $recipient) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $recipient, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $recipientEmails[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'AM'
                    ];
                }
            }

            // Compile all staff info
            $teamMembers = [];
            foreach ($request->sd as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'SD'
                    ];
                }
            }
            foreach ($request->sc as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'SC'
                    ];
                }
            }
            foreach ($request->pm as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'PM'
                    ];
                }
            }
            foreach ($request->tad as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'TAD'
                    ];
                }
            }
            foreach ($request->cm as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'CM'
                    ];
                }
            }

            DB::beginTransaction(); // Start transaction

            // Retrieve the quote by its Id.
            $quote = Quote::findOrFail($id);

            // Get the quote id
            $quoteId = $quote->quote_id;

            // Update the quote status
            $quote->status = 'pending verification';
            $quote->updated_by = auth()->user()->id;
            $quote->recipient_email = json_encode($recipientEmails);
            $quote->project_team = json_encode($teamMembers);
            $quote->save();

            // Upload all files
            $fileDescriptions = [
                // Commented out for future modifications
                // 'loa' => 'Official LOA/PO Document',
                // 'businessCase' => 'Business Case/Discount Approval Document',
                // 'arrangement' => 'Business Arrangement Form',
                'proposal' => 'Final Proposal/Quotation Document',
                'partner' => 'PO/LOA to Partner Document',
                'otherDocument' => 'Other Document'
            ];

            // Process files for each section (removed commented out sections)
            $sections = ['proposal', 'partner', 'otherDocument'];


            foreach ($sections as $section) {
                if ($request->hasFile($section)) {
                    foreach ($request->file($section) as $file) {
                        try {
                            // Check file size (10MB limit)
                            // if ($file->getSize() > 10485760) {
                            //     throw new \Exception("File {$file->getClientOriginalName()} exceeds the maximum limit of 10MB.");
                            // }
                            // Remove the existing file from the storage and database
                            $existingFiles = Attachment::where('attachable_id', $quote->id)
                                ->where('attachable_type', Quote::class)
                                ->where('description', $fileDescriptions[$section])
                                ->get();

                            foreach ($existingFiles as $existingFile) {
                                Storage::disk('public')->delete($existingFile->path);
                                $existingFile->delete();
                            }

                            // Upload the new file
                            $filename = $file->getClientOriginalName();
                            
                            // Store file in appropriate directory
                            $path = $file->storeAs('attachments', $filename, 'public');

                            // Create attachment record
                            Attachment::create([
                                'attachable_id' => $quote->id,
                                'attachable_type' => Quote::class,
                                'filename' => $filename,
                                'description' => $fileDescriptions[$section],
                                'mime_type' => $file->getClientMimeType(),
                                'path' => $path,
                                'size' => $file->getSize()
                            ]);

                        } catch (\Exception $e) {
                            DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'message' => "Error processing file: {$file->getClientOriginalName()}",
                                'error' => $e->getMessage()
                            ], 500);
                        }
                    }
                }
            }

            // Get activity
            $activity = Activity::where('trackable_id', $quote->id)
                        ->where('trackable_type', get_class($quote))
                        ->whereHas('activityType', function ($query) {
                            $query->where('name', 'Manage Return Quote');
                        })->where('status', 'in progress')->first();

            
            // Update the activity
            $activity->status = 'Done';
            $activity->actual_end_date = now();
            $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                return !$date->isWeekend();
            }, $activity->actual_start_date);
            $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false; 
            $activity->user_id = auth()->user()->id;
            $activity->save();

            DB::commit(); // Commit transaction

            $emailData = [
                'quote_id' => $quote->quote_id,
                // 'created_at' => $this->reformatDateTime($quote->created_at),
                'customer_name' => $quote->customer_name,
                'address' => $quote->address->unit_street . ', ' . $quote->address->housing_area . ', ' . $quote->address->postcode . ' ' . $quote->address->city . ', ' . $quote->address->state,
                'sfdc_id' => $quote->sfdc_id,
                // 'valid_until' => Carbon::parse($quote->valid_until, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y'),
                'view_url' => route('quote.view', ['quote' => $quote->id], true),
                'process' => 'resubmit'
            ];

            // Send email to recipient
            Mail::to(collect($teamMembers)->where('role', 'SD')->pluck('email')->toArray())
                ->cc(array_merge(
                    collect($teamMembers)->where('role', '!=', 'SD')->pluck('email')->toArray(),
                    collect($recipientEmails)->pluck('email')->toArray()
                ))
                ->send(new AcceptQuoteEmail($emailData, storage_path('app/public/quote') . '/' . 'Quote_' . $quote->quote_id . '.pdf'));

            // Log the activity
            AuditService::logActivity(
                'updated',
                get_class($quote),
                $quote->id,
                'Quote ' . $quoteId . ' is resubmitted for verification'
            );

            // Send response as a success message
            return response()->json([
                'success' => true,
                'message' => 'Quote (' . $quoteId . ') is successfully resubmitted for verification'
            ]);


        }
        catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return response()->json([
                'success' => false,
                'message' => 'Error resubmitting quote',
                'error' => $error->getMessage(),
                // add error line and file
                'line' => $error->getLine(),
                'file' => $error->getFile(),
            ], 500);
        }
    }


}
